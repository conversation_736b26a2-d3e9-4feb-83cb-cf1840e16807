export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
}
export interface ApiError {
    code: string;
    message: string;
    details?: any;
}
export interface PaginatedResponse<T> {
    data: T[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
}
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
export interface RequestConfig {
    method?: HttpMethod;
    headers?: Record<string, string>;
    body?: any;
    timeout?: number;
}
export interface FireGeoApiEndpoints {
    auth: {
        bridge: '/api/auth/bridge';
        session: '/api/auth/session';
    };
    brandMonitor: {
        analyses: '/api/brand-monitor/analyses';
        analyze: '/api/brand-monitor/analyze';
        scrape: '/api/brand-monitor/scrape';
        checkProviders: '/api/brand-monitor/check-providers';
    };
    user: {
        profile: '/api/user/profile';
        settings: '/api/user/settings';
    };
}
