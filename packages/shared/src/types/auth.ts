// Authentication types for Supabase to FireGeo bridge
export interface SupabaseUser {
  id: string
  email: string
  user_metadata?: {
    full_name?: string
    avatar_url?: string
  }
}

export interface FireGeoAuthContext {
  userId: string
  email: string
  name?: string
  avatarUrl?: string
}

export interface AuthBridgeRequest {
  supabaseToken: string
  user: SupabaseUser
}

export interface AuthBridgeResponse {
  success: boolean
  session?: FireGeoSession
  user?: FireGeoAuthContext
  error?: string
}

export interface FireGeoSession {
  user: {
    id: string
    email: string
    name: string
    image?: string
  }
}
