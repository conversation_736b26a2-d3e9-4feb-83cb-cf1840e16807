// Brand monitor types for FireGeo integration
export interface BrandAnalysis {
  id: string
  url: string
  companyName?: string
  industry?: string
  analysisData?: any
  competitors?: IdentifiedCompetitor[]
  prompts?: string[]
  creditsUsed?: number
  createdAt: string
  updatedAt: string
}

export interface IdentifiedCompetitor {
  id: string
  name: string
  url?: string
  industry?: string
}

export interface Company {
  id?: string
  name: string
  url: string
  industry?: string
  description?: string
}

export interface AnalysisProgress {
  step: string
  progress: number
  message: string
  completed: boolean
}

export interface BrandAnalysisRequest {
  url?: string // For backward compatibility
  companyName?: string // For backward compatibility
  company?: Company // New format - company object
  competitors?: string[] | { name: string }[] // Support both formats
  prompts?: string[]
  useWebSearch?: boolean
}

export interface BrandAnalysisResponse {
  id: string
  url: string
  companyName?: string
  status: 'pending' | 'in_progress' | 'completed' | 'failed'
  progress?: AnalysisProgress
  data?: any
  error?: string
  createdAt: string
  updatedAt: string
}
