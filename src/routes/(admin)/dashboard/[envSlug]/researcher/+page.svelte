<script lang="ts">
  import { writable } from "svelte/store"
  import { page } from "$app/stores"
  import { onMount } from "svelte"
  import { ChartBar, Target, Brain } from "lucide-svelte"

  // Import new components
  import ResearchSidebar from "$lib/components/research/ResearchSidebar.svelte"
  import ResearchChatArea from "$lib/components/research/ResearchChatArea.svelte"
  import ProjectsSidebar from "$lib/components/research/ProjectsSidebar.svelte"

  // Get data from layout
  let { data } = $props()
  let { session, profile } = data

  interface Message {
    id: string
    role: "user" | "assistant"
    content: string
    timestamp: Date
    isReport?: boolean
  }

  interface ProgressStep {
    id: number
    title: string
    description: string
    status: "pending" | "active" | "completed"
    progress?: number
  }

  const messages = writable<Message[]>([])
  let input = $state("")
  let isLoading = $state(false)
  // let selectedMessageId = "" // Not currently used
  let outputFormat = $state("executive")
  // let agentMode = "general" // Removed agent mode functionality
  let progressSteps = $state<ProgressStep[]>([])
  let currentProgress = $state(0)

  // Sidebar collapse state
  let leftSidebarCollapsed = $state(false)
  let rightSidebarCollapsed = $state(false)

  // Responsive behavior - auto-collapse on mobile
  let innerWidth = $state(0)
  let isMobile = $derived(innerWidth < 768)

  $effect(() => {
    if (isMobile) {
      leftSidebarCollapsed = true
      rightSidebarCollapsed = true
    }
  })


  // Quick start templates
  const quickStartTemplates = [
    {
      icon: ChartBar,
      title: "Company Snapshot",
      description: "Get a summary of performance, team, and competitors",
      prompt:
        "Provide a comprehensive company snapshot for [Company Name], including recent financial performance, leadership team overview, main competitors, and key business metrics.",
    },
    {
      icon: Target,
      title: "Go-to-Market Audit",
      description: "Evaluate positioning, messaging, channels, and campaigns",
      prompt:
        "Analyze [Company Name]'s go-to-market strategy including their positioning, messaging, marketing channels, recent campaigns, and overall effectiveness in reaching their target audience.",
    },
    {
      icon: Brain,
      title: "Brand Perception & Category",
      description: "Analyze how a brand is perceived in its space",
      prompt:
        "Research [Company Name]'s brand perception, category positioning, competitive differentiation, and customer sentiment. Include analysis of their brand identity and market perception.",
    },
  ]

  // Removed filter options - functionality not needed

  function generateId(): string {
    return Math.random().toString(36).substr(2, 9)
  }

  async function sendMessage() {
    if (!input.trim() || isLoading) return

    // Add output format context to the message with enhanced format-specific instructions
    let formatPrefix = ""
    if (outputFormat === "executive") {
      formatPrefix =
        "[Executive Summary Format] Provide concise bullet points, key insights, and executive-level highlights. Focus on high-level strategic information that executives need to know quickly. Use clear sections and numbered citations. "
    } else if (outputFormat === "slide-ready") {
      formatPrefix =
        "[Slide-ready Format] Structure with clear sections and headers suitable for presentation export. Use numbered sections, clear headings, and bullet points that can be easily converted to slides. Include numbered citations for all claims. "
    } else if (outputFormat === "battlecard") {
      formatPrefix =
        "[Competitive Battlecard Format] Focus on strategic comparison and competitive positioning. Emphasize competitive advantages, market differentiation, and head-to-head comparisons. Include numbered citations and competitive analysis. "
    }

    const userMessage = formatPrefix + input.trim()
    input = ""
    isLoading = true

    // Initialize progress steps
    progressSteps = [
      {
        id: 1,
        title: "Initial Analysis",
        description: "Analyzing research request...",
        status: "pending",
      },
      {
        id: 2,
        title: "Web Search",
        description: "Conducting comprehensive search...",
        status: "pending",
      },
      {
        id: 3,
        title: "Financial Analysis",
        description: "Gathering financial data...",
        status: "pending",
      },
      {
        id: 4,
        title: "Market Research",
        description: "Analyzing market position...",
        status: "pending",
      },
      {
        id: 5,
        title: "Report Generation",
        description: "Creating research report...",
        status: "pending",
      },
    ]
    currentProgress = 0

    // Add user message to chat
    messages.update((msgs) => [
      ...msgs,
      {
        id: generateId(),
        role: "user",
        content: userMessage,
        timestamp: new Date(),
      },
    ])

    try {
      // Use streaming for progress updates
      const response = await fetch(
        `/dashboard/${$page.params.envSlug}/researcher?stream=true`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ message: userMessage }),
        },
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (reader) {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const lines = chunk.split("\n")

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              try {
                const data = JSON.parse(line.slice(6))

                if (data.type === "final_response") {
                  // Add assistant response to chat
                  const assistantMessageId = generateId()
                  messages.update((msgs) => [
                    ...msgs,
                    {
                      id: assistantMessageId,
                      role: "assistant",
                      content: data.response,
                      timestamp: new Date(),
                      isReport: true,
                    },
                  ])
                } else if (data.step) {
                  // Update progress
                  currentProgress = data.progress
                  progressSteps = progressSteps.map((step) => {
                    if (step.id === data.step) {
                      return {
                        ...step,
                        status: data.status,
                        description: data.action,
                      }
                    } else if (step.id < data.step) {
                      return { ...step, status: "completed" }
                    }
                    return step
                  })
                }
              } catch (e) {
                console.error("Error parsing SSE data:", e)
              }
            }
          }
        }
      }
    } catch (error) {
      console.error("Error sending message:", error)
      messages.update((msgs) => [
        ...msgs,
        {
          id: generateId(),
          role: "assistant",
          content:
            "I apologize, but Compass encountered an error while processing your request. Please try again.",
          timestamp: new Date(),
        },
      ])
    } finally {
      isLoading = false
      // Reset progress
      progressSteps = []
      currentProgress = 0
    }
  }


  function handleTemplateClick(template: (typeof quickStartTemplates)[0]) {
    input = template.prompt
    // Focus the textarea
    const textarea = document.querySelector("textarea")
    if (textarea) {
      textarea.focus()
    }
  }

  // Removed toggleFilters function - not needed




  // Auto-scroll to bottom when new messages are added (modern layout)
  $effect(() => {
    if ($messages.length > 0) {
      setTimeout(() => {
        // Scroll to the bottom of the page to show the latest message
        window.scrollTo({
          top: document.body.scrollHeight,
          behavior: "smooth",
        })
      }, 100)
    }
  })

  // Removed - replaced with quickStartTemplates
</script>

<svelte:head>
  <title>Compass - AI Market Researcher</title>
</svelte:head>

<!-- Window width binding for responsive behavior -->
<svelte:window bind:innerWidth />

<!-- New Three-Column Layout -->
<div class="flex h-screen bg-background text-foreground">
  <!-- Left Sidebar -->
  <ResearchSidebar
    bind:collapsed={leftSidebarCollapsed}
    {isMobile}
    {session}
    {profile}
  />

  <!-- Main Content Area -->
  <ResearchChatArea
    messages={$messages}
    bind:input
    bind:isLoading  
    bind:progressSteps
    bind:currentProgress
    {leftSidebarCollapsed}
    {rightSidebarCollapsed}
  />

  <!-- Right Sidebar -->
  <ProjectsSidebar 
    bind:collapsed={rightSidebarCollapsed} 
    {isMobile} 
    promptTemplates={quickStartTemplates}
    onTemplateClick={handleTemplateClick}
  />
</div>
