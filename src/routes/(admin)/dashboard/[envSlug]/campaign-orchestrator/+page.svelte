<script lang="ts">
  import { writable } from "svelte/store"
  import { page } from "$app/stores"
  import {
    Megaphone,
    Download,
    Calendar,
    Users,
    Clock,
    User,
    Bot,
    ChevronRight,
    Copy,
    Zap,
    Mail,
    Share2,
    CheckCircle2,
    Loader2,
    Circle,
  } from "lucide-svelte"
  import { onMount } from "svelte"
  import { slide, fade } from "svelte/transition"

  interface Message {
    id: string
    role: "user" | "assistant"
    content: string
    timestamp: Date
    isReport?: boolean
    data?: any
  }

  interface ProgressStep {
    id: number
    title: string
    description: string
    status: "pending" | "active" | "completed"
    progress?: number
  }

  const messages = writable<Message[]>([])
  let input = ""
  let isLoading = false
  let placeholderIndex = 0
  let currentPlaceholder = ""
  let progressSteps: ProgressStep[] = []
  let currentProgress = 0

  // Animated placeholder examples for company research
  const placeholderExamples = [
    "coreweave.com",
    "singlestore.com",
    "CoreWeave",
    "SingleStore",
    "research stripe.com",
    "analyze openai.com",
    "investigate anthropic.com",
  ]

  // Interactive card templates for company research
  const interactiveCards = [
    {
      icon: Zap,
      title: "Competitor Analysis",
      description: "Deep dive into competitor landscape and market positioning",
      prompt: "coreweave.com",
    },
    {
      icon: Calendar,
      title: "Market Research",
      description: "Comprehensive company intelligence with contact discovery",
      prompt: "SingleStore",
    },
    {
      icon: Users,
      title: "Lead Generation",
      description: "Find similar companies and key decision-maker contacts",
      prompt: "research stripe.com",
    },
  ]

  function generateId(): string {
    return Math.random().toString(36).substring(2, 11)
  }

  async function sendMessage() {
    if (!input.trim() || isLoading) return

    const userMessage = input.trim()
    input = ""
    isLoading = true

    // Initialize progress steps for company research
    progressSteps = [
      {
        id: 1,
        title: "Input Processing",
        description: "Processing company information...",
        status: "pending",
      },
      {
        id: 2,
        title: "Company Enrichment",
        description: "Enriching target company data...",
        status: "pending",
      },
      {
        id: 3,
        title: "Intelligence Gathering",
        description: "Finding competitors and insights...",
        status: "pending",
      },
      {
        id: 4,
        title: "Company Discovery",
        description: "Discovering similar companies...",
        status: "pending",
      },
      {
        id: 5,
        title: "Contact Retrieval",
        description: "Retrieving contact information...",
        status: "pending",
      },
      {
        id: 6,
        title: "Results Formatting",
        description: "Formatting comprehensive results...",
        status: "pending",
      },
    ]
    currentProgress = 0

    // Add user message to chat
    messages.update((msgs) => [
      ...msgs,
      {
        id: generateId(),
        role: "user",
        content: userMessage,
        timestamp: new Date(),
      },
    ])

    try {
      // Use streaming for progress updates
      const response = await fetch(
        `/dashboard/${$page.params.envSlug}/campaign-orchestrator?stream=true`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ message: userMessage }),
        },
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (reader) {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const lines = chunk.split("\n")

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              try {
                const data = JSON.parse(line.slice(6))

                if (data.step && data.step > 0) {
                  // Update progress
                  currentProgress = data.progress || 0

                  // Update step status
                  progressSteps = progressSteps.map((step) => {
                    if (step.id < data.step) {
                      return { ...step, status: "completed" }
                    } else if (step.id === data.step) {
                      return {
                        ...step,
                        status: "active",
                        description: data.action,
                      }
                    }
                    return step
                  })
                }

                if (data.status === "completed" && data.response) {
                  console.log("Received completed response:", {
                    response: data.response,
                    data: data.data,
                    hasData: !!data.data,
                  })

                  // Add assistant response with structured data from server
                  messages.update((msgs) => [
                    ...msgs,
                    {
                      id: generateId(),
                      role: "assistant",
                      content: data.response,
                      timestamp: new Date(),
                      data: data.data || null,
                      isReport: true,
                    },
                  ])
                }

                if (data.status === "error") {
                  throw new Error(data.error || "Unknown error occurred")
                }
              } catch (parseError) {
                console.warn("Failed to parse SSE data:", parseError)
              }
            }
          }
        }
      }
    } catch (error) {
      console.error("Campaign Orchestrator error:", error)
      messages.update((msgs) => [
        ...msgs,
        {
          id: generateId(),
          role: "assistant",
          content: `Error: ${error instanceof Error ? error.message : "Unknown error occurred"}`,
          timestamp: new Date(),
          isReport: true,
        },
      ])
    } finally {
      isLoading = false
      progressSteps = progressSteps.map((step) => ({
        ...step,
        status: "completed",
      }))
      currentProgress = 100
    }
  }

  function handleKeyDown(e: KeyboardEvent) {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  function formatContent(content: string): string {
    // Basic markdown to HTML conversion
    return content
      .replace(
        /^### (.+)$/gm,
        '<h3 class="text-lg font-bold mt-4 mb-2">$1</h3>',
      )
      .replace(/^## (.+)$/gm, '<h2 class="text-xl font-bold mt-6 mb-3">$1</h2>')
      .replace(/^# (.+)$/gm, '<h1 class="text-2xl font-bold mb-4">$1</h1>')
      .replace(/^\* (.+)$/gm, '<li class="ml-4">• $1</li>')
      .replace(/^- (.+)$/gm, '<li class="ml-4">• $1</li>')
      .replace(/\*\*(.+?)\*\*/g, "<strong>$1</strong>")
      .replace(/\n\n/g, '</p><p class="mb-4">')
      .replace(/^/, '<p class="mb-4">')
      .replace(/$/, "</p>")
  }

  function copyToClipboard(text: string) {
    navigator.clipboard.writeText(text)
  }

  function downloadAsMarkdown(message: Message) {
    const blob = new Blob([message.content], { type: "text/markdown" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `campaign-plan-${new Date().toISOString().split("T")[0]}.md`
    a.click()
  }

  // Update placeholder animation
  onMount(() => {
    currentPlaceholder = placeholderExamples[0]
    const interval = setInterval(() => {
      placeholderIndex = (placeholderIndex + 1) % placeholderExamples.length
      currentPlaceholder = placeholderExamples[placeholderIndex]
    }, 3000)
    return () => clearInterval(interval)
  })

  // Simulate progress updates
  let progressInterval: NodeJS.Timeout | undefined

  $: if (isLoading && progressSteps.length > 0) {
    progressInterval = setInterval(() => {
      const pendingStep = progressSteps.find(
        (step) => step.status === "pending",
      )
      const activeStep = progressSteps.find((step) => step.status === "active")

      if (activeStep && activeStep.progress !== undefined) {
        activeStep.progress = Math.min(100, activeStep.progress + 20)
        if (activeStep.progress === 100) {
          activeStep.status = "completed"
          currentProgress = Math.round(
            (progressSteps.filter((s) => s.status === "completed").length /
              progressSteps.length) *
              100,
          )
        }
      } else if (pendingStep) {
        pendingStep.status = "active"
        pendingStep.progress = 0
      } else {
        clearInterval(progressInterval)
      }
      progressSteps = [...progressSteps]
    }, 300)
  } else if (progressInterval) {
    clearInterval(progressInterval)
  }

  // Auto-scroll to bottom when new messages are added (modern layout)
  $: if ($messages.length > 0) {
    setTimeout(() => {
      // Scroll to the bottom of the page to show the latest message
      window.scrollTo({
        top: document.body.scrollHeight,
        behavior: "smooth",
      })
    }, 100)
  }
</script>

<svelte:head>
  <title>Catalyst - AI Company Research & Intelligence</title>
  <meta
    name="description"
    content="Comprehensive company research with competitor analysis and contact discovery powered by AI"
  />
</svelte:head>

<!-- Modern Full-Page Chat Layout -->
<div class="modern-chat-container">
  <!-- Header -->
  <div
    class="border-b-2 flex-shrink-0"
    style="border-color: var(--border); background: var(--background);"
  >
    <div class="max-w-7xl mx-auto px-6 lg:px-8 py-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div
            class="w-12 h-12 flex items-center justify-center border-2 hover:scale-105 transition-transform cursor-pointer"
            style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"
          >
            <Megaphone
              class="w-6 h-6 animate-pulse"
              style="color: var(--primary-foreground);"
            />
          </div>
          <div>
            <h1 class="text-3xl font-black" style="color: var(--foreground);">
              Catalyst
            </h1>
            <p
              class="text-lg font-medium"
              style="color: var(--muted-foreground);"
            >
              AI-powered company research with competitor analysis and contact
              discovery
            </p>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <div
            class="flex items-center space-x-2 px-4 py-2 border-2"
            style="background: var(--accent); border-color: var(--border); box-shadow: var(--shadow-sm);"
          >
            <Share2 class="w-4 h-4" style="color: var(--accent-foreground);" />
            <span
              class="text-sm font-bold"
              style="color: var(--accent-foreground);">Multi-Channel</span
            >
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Breadcrumb Navigation -->
  <div class="max-w-7xl px-6 lg:px-8 py-4">
    <nav class="flex items-center space-x-2 text-sm text-muted-foreground">
      <a
        href="/dashboard/{$page.params.envSlug}"
        class="hover:text-foreground transition-colors"
      >
        Dashboard
      </a>
      <ChevronRight class="w-4 h-4" />
      <span class="text-foreground font-medium">Catalyst</span>
    </nav>
  </div>

  <!-- Messages Area - Full Page Flow -->
  <div class="modern-messages-area">
    <div class="modern-message-container">
      <div class="space-y-6">
        {#if $messages.length === 0}
          <div class="text-center py-12">
            <div
              class="w-16 h-16 mx-auto mb-4 flex items-center justify-center border-2"
              style="background: var(--muted); border-color: var(--border);"
            >
              <Megaphone
                class="w-8 h-8"
                style="color: var(--muted-foreground);"
              />
            </div>
            <h3
              class="text-xl font-bold mb-2"
              style="color: var(--foreground);"
            >
              Start Your Company Research
            </h3>
            <p class="font-medium mb-6" style="color: var(--muted-foreground);">
              Get comprehensive company intelligence with competitor analysis
              and contacts
            </p>

            <!-- Interactive Cards -->
            <div class="grid md:grid-cols-3 gap-4 max-w-4xl mx-auto">
              {#each interactiveCards as card}
                <button
                  on:click={() => {
                    input = card.prompt
                  }}
                  class="card-brutal p-4 text-left transition-all duration-200 hover:-translate-x-1 hover:-translate-y-1 group"
                  style="background: var(--card); border-color: var(--border);"
                  disabled={isLoading}
                >
                  <div class="flex items-center gap-3 mb-3">
                    <div
                      class="w-8 h-8 flex items-center justify-center border-2 group-hover:scale-110 transition-transform"
                      style="background: var(--primary); border-color: var(--border);"
                    >
                      <svelte:component
                        this={card.icon}
                        class="w-4 h-4"
                        style="color: var(--primary-foreground);"
                      />
                    </div>
                    <h4
                      class="font-bold text-sm"
                      style="color: var(--foreground);"
                    >
                      {card.title}
                    </h4>
                  </div>
                  <p
                    class="text-xs mb-3"
                    style="color: var(--muted-foreground);"
                  >
                    {card.description}
                  </p>
                  <div
                    class="text-xs font-mono p-2 border-2 rounded"
                    style="background: var(--muted); border-color: var(--border); color: var(--muted-foreground);"
                  >
                    {card.prompt}
                  </div>
                </button>
              {/each}
            </div>
          </div>
        {/if}

        {#each $messages as message}
          <div
            class="modern-message-item flex gap-4 {message.role === 'user'
              ? 'flex-row-reverse'
              : ''}"
          >
            <div
              class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2"
              style="background: var(--{message.role === 'user'
                ? 'primary'
                : 'secondary'}); border-color: var(--border); box-shadow: var(--shadow-sm);"
            >
              {#if message.role === "user"}
                <User
                  class="w-5 h-5"
                  style="color: var(--primary-foreground);"
                />
              {:else}
                <Bot
                  class="w-5 h-5"
                  style="color: var(--secondary-foreground);"
                />
              {/if}
            </div>

            <div class="flex-1 max-w-3xl">
              <div class="flex items-center gap-2 mb-2">
                <span
                  class="text-sm font-bold"
                  style="color: var(--foreground);"
                >
                  {message.role === "user" ? "You" : "Catalyst"}
                </span>
                <div class="flex items-center gap-1">
                  <Clock
                    class="w-3 h-3"
                    style="color: var(--muted-foreground);"
                  />
                  <span class="text-xs" style="color: var(--muted-foreground);">
                    {message.timestamp.toLocaleTimeString()}
                  </span>
                </div>
                {#if message.role === "assistant" && message.isReport}
                  <button
                    on:click={() => downloadAsMarkdown(message)}
                    class="btn-secondary px-2 py-1 text-xs flex items-center gap-1"
                    title="Download as Markdown"
                  >
                    <Download class="w-3 h-3" />
                    Download
                  </button>
                {/if}
              </div>

              {#if message.role === "user"}
                <div
                  class="p-4 border-2"
                  style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"
                >
                  <p
                    class="font-medium"
                    style="color: var(--primary-foreground);"
                  >
                    {message.content}
                  </p>
                </div>
              {:else}
                <div
                  class="p-6 border-2 mb-4"
                  style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow);"
                >
                  {#if message.data && (message.data.targetCompany || message.data.competitors)}
                    <!-- Professional Company Research Report -->
                    <div class="space-y-6">
                      <!-- Executive Summary Header -->
                      <div
                        class="text-center border-b-2 pb-4"
                        style="border-color: var(--border);"
                      >
                        <h2
                          class="text-2xl font-black mb-2"
                          style="color: var(--foreground);"
                        >
                          Company Intelligence Report
                        </h2>
                        <p
                          class="text-sm"
                          style="color: var(--muted-foreground);"
                        >
                          Comprehensive analysis with competitor landscape and
                          contact intelligence
                        </p>
                      </div>

                      {#if message.data.targetCompany}
                        <!-- Target Company Section -->
                        <div
                          class="border-2 overflow-hidden"
                          style="border-color: var(--border); box-shadow: var(--shadow); border-radius: var(--radius);"
                        >
                          <div class="p-4" style="background: var(--primary);">
                            <h3
                              class="text-lg font-bold flex items-center"
                              style="color: var(--primary-foreground);"
                            >
                              <Megaphone class="w-5 h-5 mr-2" />
                              Target Company Profile
                            </h3>
                          </div>
                          <div
                            class="p-6"
                            style="background: var(--background);"
                          >
                            <!-- Company Header -->
                            <div class="flex items-start justify-between mb-4">
                              <div>
                                <h4
                                  class="text-xl font-bold"
                                  style="color: var(--foreground);"
                                >
                                  {message.data.targetCompany.name ||
                                    "Unknown Company"}
                                </h4>
                                <p
                                  class="text-sm font-medium"
                                  style="color: var(--muted-foreground);"
                                >
                                  {message.data.targetCompany.domain ||
                                    "No domain available"}
                                </p>
                              </div>
                              {#if message.data.targetCompany.founded_year}
                                <div class="text-right">
                                  <div
                                    class="text-xs"
                                    style="color: var(--muted-foreground);"
                                  >
                                    Founded
                                  </div>
                                  <div
                                    class="font-bold"
                                    style="color: var(--foreground);"
                                  >
                                    {message.data.targetCompany.founded_year}
                                  </div>
                                </div>
                              {/if}
                            </div>

                            <!-- Key Metrics Grid -->
                            <div
                              class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4"
                            >
                              <div
                                class="p-3 border-2 rounded"
                                style="background: var(--muted); border-color: var(--border);"
                              >
                                <div
                                  class="text-xs font-medium mb-1"
                                  style="color: var(--muted-foreground);"
                                >
                                  Industry
                                </div>
                                <div
                                  class="font-bold"
                                  style="color: var(--foreground);"
                                >
                                  {message.data.targetCompany.industry ||
                                    "Not specified"}
                                </div>
                              </div>
                              <div
                                class="p-3 border-2 rounded"
                                style="background: var(--muted); border-color: var(--border);"
                              >
                                <div
                                  class="text-xs font-medium mb-1"
                                  style="color: var(--muted-foreground);"
                                >
                                  Employees
                                </div>
                                <div
                                  class="font-bold"
                                  style="color: var(--foreground);"
                                >
                                  {message.data.targetCompany.employees ||
                                    "Unknown"}
                                </div>
                              </div>
                              <div
                                class="p-3 border-2 rounded"
                                style="background: var(--muted); border-color: var(--border);"
                              >
                                <div
                                  class="text-xs font-medium mb-1"
                                  style="color: var(--muted-foreground);"
                                >
                                  Revenue
                                </div>
                                <div
                                  class="font-bold"
                                  style="color: var(--foreground);"
                                >
                                  {message.data.targetCompany.revenue ||
                                    "Not disclosed"}
                                </div>
                              </div>
                            </div>

                            <!-- Company Description -->
                            {#if message.data.targetCompany.description}
                              <div
                                class="p-4 border-2 rounded"
                                style="background: var(--card); border-color: var(--border);"
                              >
                                <h5
                                  class="font-bold mb-2"
                                  style="color: var(--foreground);"
                                >
                                  Company Overview
                                </h5>
                                <p
                                  class="text-sm leading-relaxed"
                                  style="color: var(--muted-foreground);"
                                >
                                  {message.data.targetCompany.description}
                                </p>
                              </div>
                            {/if}

                            <!-- Contact Information -->
                            {#if message.data.targetCompany.contacts && message.data.targetCompany.contacts.length > 0}
                              <div class="mt-4">
                                <h5
                                  class="font-bold mb-3 flex items-center"
                                  style="color: var(--foreground);"
                                >
                                  <Users class="w-4 h-4 mr-2" />
                                  Key Contacts ({message.data.targetCompany
                                    .contacts.length})
                                </h5>
                                <div class="grid gap-3">
                                  {#each message.data.targetCompany.contacts as contact}
                                    <div
                                      class="p-3 border-2 rounded flex items-center justify-between"
                                      style="background: var(--muted); border-color: var(--border);"
                                    >
                                      <div>
                                        <div
                                          class="font-medium"
                                          style="color: var(--foreground);"
                                        >
                                          {contact.name ||
                                            `${contact.first_name || ""} ${contact.last_name || ""}`.trim() ||
                                            "Unknown"}
                                        </div>
                                        <div
                                          class="text-sm"
                                          style="color: var(--muted-foreground);"
                                        >
                                          {contact.title ||
                                            "No title available"}
                                        </div>
                                      </div>
                                      {#if contact.email}
                                        <div
                                          class="text-xs font-mono p-1 border rounded"
                                          style="background: var(--background); border-color: var(--border); color: var(--muted-foreground);"
                                        >
                                          {contact.email}
                                        </div>
                                      {/if}
                                    </div>
                                  {/each}
                                </div>
                              </div>
                            {/if}
                          </div>
                        </div>
                      {/if}

                      {#if message.data.competitors && message.data.competitors.length > 0}
                        <!-- Competitive Landscape Section -->
                        <div
                          class="border-2 overflow-hidden"
                          style="border-color: var(--border); box-shadow: var(--shadow); border-radius: var(--radius);"
                        >
                          <div
                            class="p-4"
                            style="background: var(--secondary);"
                          >
                            <h3
                              class="text-lg font-bold flex items-center"
                              style="color: var(--secondary-foreground);"
                            >
                              <Users class="w-5 h-5 mr-2" />
                              Competitive Landscape ({message.data.competitors
                                .length} companies)
                            </h3>
                          </div>
                          <div
                            class="p-6"
                            style="background: var(--background);"
                          >
                            <div class="grid gap-4">
                              {#each message.data.competitors.slice(0, 8) as competitor, index}
                                <div
                                  class="border-2 rounded-lg overflow-hidden"
                                  style="border-color: var(--border);"
                                >
                                  <div class="p-4">
                                    <div
                                      class="flex items-start justify-between mb-3"
                                    >
                                      <div class="flex-1">
                                        <div
                                          class="flex items-center gap-2 mb-1"
                                        >
                                          <span
                                            class="text-xs font-bold px-2 py-1 rounded"
                                            style="background: var(--accent); color: var(--accent-foreground);"
                                          >
                                            #{index + 1}
                                          </span>
                                          <h4
                                            class="text-lg font-bold"
                                            style="color: var(--foreground);"
                                          >
                                            {competitor.name ||
                                              "Unknown Company"}
                                          </h4>
                                        </div>
                                        <p
                                          class="text-sm font-medium mb-2"
                                          style="color: var(--muted-foreground);"
                                        >
                                          {competitor.domain ||
                                            "No domain available"}
                                        </p>
                                        {#if competitor.description}
                                          <p
                                            class="text-sm leading-relaxed"
                                            style="color: var(--muted-foreground);"
                                          >
                                            {competitor.description.length > 150
                                              ? competitor.description.substring(
                                                  0,
                                                  150,
                                                ) + "..."
                                              : competitor.description}
                                          </p>
                                        {/if}
                                      </div>
                                      <div class="text-right ml-4">
                                        <div
                                          class="text-xs mb-1"
                                          style="color: var(--muted-foreground);"
                                        >
                                          Similarity
                                        </div>
                                        <div
                                          class="text-sm font-bold px-2 py-1 border rounded"
                                          style="background: var(--muted); border-color: var(--border); color: var(--foreground);"
                                        >
                                          {competitor.similarity || "Similar"}
                                        </div>
                                      </div>
                                    </div>

                                    <!-- Competitor Metrics -->
                                    <div
                                      class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3"
                                    >
                                      {#if competitor.industry}
                                        <div
                                          class="text-center p-2 border rounded"
                                          style="background: var(--muted); border-color: var(--border);"
                                        >
                                          <div
                                            class="text-xs"
                                            style="color: var(--muted-foreground);"
                                          >
                                            Industry
                                          </div>
                                          <div
                                            class="text-sm font-medium"
                                            style="color: var(--foreground);"
                                          >
                                            {competitor.industry}
                                          </div>
                                        </div>
                                      {/if}
                                      {#if competitor.employees}
                                        <div
                                          class="text-center p-2 border rounded"
                                          style="background: var(--muted); border-color: var(--border);"
                                        >
                                          <div
                                            class="text-xs"
                                            style="color: var(--muted-foreground);"
                                          >
                                            Employees
                                          </div>
                                          <div
                                            class="text-sm font-medium"
                                            style="color: var(--foreground);"
                                          >
                                            {competitor.employees}
                                          </div>
                                        </div>
                                      {/if}
                                      {#if competitor.location}
                                        <div
                                          class="text-center p-2 border rounded"
                                          style="background: var(--muted); border-color: var(--border);"
                                        >
                                          <div
                                            class="text-xs"
                                            style="color: var(--muted-foreground);"
                                          >
                                            Location
                                          </div>
                                          <div
                                            class="text-sm font-medium"
                                            style="color: var(--foreground);"
                                          >
                                            {competitor.location}
                                          </div>
                                        </div>
                                      {/if}
                                      {#if competitor.market_position}
                                        <div
                                          class="text-center p-2 border rounded"
                                          style="background: var(--muted); border-color: var(--border);"
                                        >
                                          <div
                                            class="text-xs"
                                            style="color: var(--muted-foreground);"
                                          >
                                            Position
                                          </div>
                                          <div
                                            class="text-sm font-medium"
                                            style="color: var(--foreground);"
                                          >
                                            {competitor.market_position}
                                          </div>
                                        </div>
                                      {/if}
                                    </div>

                                    <!-- Competitor Contacts -->
                                    {#if competitor.contacts && competitor.contacts.length > 0}
                                      <div
                                        class="border-t pt-3"
                                        style="border-color: var(--border);"
                                      >
                                        <h6
                                          class="text-sm font-bold mb-2"
                                          style="color: var(--foreground);"
                                        >
                                          Key Contacts ({competitor.contacts
                                            .length})
                                        </h6>
                                        <div class="grid gap-2">
                                          {#each competitor.contacts.slice(0, 3) as contact}
                                            <div
                                              class="flex items-center justify-between p-2 border rounded"
                                              style="background: var(--card); border-color: var(--border);"
                                            >
                                              <div>
                                                <div
                                                  class="text-sm font-medium"
                                                  style="color: var(--foreground);"
                                                >
                                                  {contact.name ||
                                                    `${contact.first_name || ""} ${contact.last_name || ""}`.trim() ||
                                                    "Unknown"}
                                                </div>
                                                <div
                                                  class="text-xs"
                                                  style="color: var(--muted-foreground);"
                                                >
                                                  {contact.title || "No title"}
                                                </div>
                                              </div>
                                              {#if contact.email}
                                                <div
                                                  class="text-xs font-mono px-2 py-1 border rounded"
                                                  style="background: var(--background); border-color: var(--border); color: var(--muted-foreground);"
                                                >
                                                  {contact.email}
                                                </div>
                                              {/if}
                                            </div>
                                          {/each}
                                        </div>
                                      </div>
                                    {/if}
                                  </div>
                                </div>
                              {/each}
                            </div>
                          </div>
                        </div>
                      {/if}

                      <!-- Intelligence Summary Section -->
                      {#if message.data.intelligence}
                        <div
                          class="border-2 rounded-lg overflow-hidden"
                          style="border-color: var(--border); box-shadow: var(--shadow);"
                        >
                          <div class="p-4" style="background: var(--accent);">
                            <h3
                              class="text-lg font-bold flex items-center"
                              style="color: var(--accent-foreground);"
                            >
                              <Megaphone class="w-5 h-5 mr-2" />
                              Market Intelligence Summary
                            </h3>
                          </div>
                          <div
                            class="p-6"
                            style="background: var(--background);"
                          >
                            <div class="grid md:grid-cols-3 gap-6">
                              {#if message.data.intelligence.market_insights}
                                <div>
                                  <h4
                                    class="font-bold mb-2"
                                    style="color: var(--foreground);"
                                  >
                                    Market Insights
                                  </h4>
                                  <p
                                    class="text-sm leading-relaxed"
                                    style="color: var(--muted-foreground);"
                                  >
                                    {message.data.intelligence.market_insights}
                                  </p>
                                </div>
                              {/if}
                              {#if message.data.intelligence.competitive_landscape}
                                <div>
                                  <h4
                                    class="font-bold mb-2"
                                    style="color: var(--foreground);"
                                  >
                                    Competitive Landscape
                                  </h4>
                                  <p
                                    class="text-sm leading-relaxed"
                                    style="color: var(--muted-foreground);"
                                  >
                                    {message.data.intelligence
                                      .competitive_landscape}
                                  </p>
                                </div>
                              {/if}
                              {#if message.data.intelligence.key_differentiators}
                                <div>
                                  <h4
                                    class="font-bold mb-2"
                                    style="color: var(--foreground);"
                                  >
                                    Key Differentiators
                                  </h4>
                                  <p
                                    class="text-sm leading-relaxed"
                                    style="color: var(--muted-foreground);"
                                  >
                                    {message.data.intelligence
                                      .key_differentiators}
                                  </p>
                                </div>
                              {/if}
                            </div>
                          </div>
                        </div>
                      {/if}

                      <!-- Research Metadata -->
                      {#if message.data.metadata}
                        <div
                          class="border-2 rounded-lg p-4"
                          style="border-color: var(--border); background: var(--muted);"
                        >
                          <h4
                            class="font-bold mb-3"
                            style="color: var(--foreground);"
                          >
                            Research Summary
                          </h4>
                          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div class="text-center">
                              <div
                                class="text-2xl font-black mb-1"
                                style="color: var(--primary);"
                              >
                                {message.data.metadata.totalCompanies || 0}
                              </div>
                              <div
                                class="text-xs font-medium"
                                style="color: var(--muted-foreground);"
                              >
                                Companies Analyzed
                              </div>
                            </div>
                            <div class="text-center">
                              <div
                                class="text-2xl font-black mb-1"
                                style="color: var(--primary);"
                              >
                                {message.data.metadata.totalContacts || 0}
                              </div>
                              <div
                                class="text-xs font-medium"
                                style="color: var(--muted-foreground);"
                              >
                                Contacts Found
                              </div>
                            </div>
                            <div class="text-center">
                              <div
                                class="text-sm font-bold px-2 py-1 border rounded"
                                style="background: var(--background); border-color: var(--border); color: var(--foreground);"
                              >
                                {message.data.metadata.dataQuality || "N/A"}
                              </div>
                              <div
                                class="text-xs font-medium mt-1"
                                style="color: var(--muted-foreground);"
                              >
                                Data Quality
                              </div>
                            </div>
                            <div class="text-center">
                              <div
                                class="text-sm font-bold"
                                style="color: var(--foreground);"
                              >
                                {message.data.metadata.processingTime || "N/A"}
                              </div>
                              <div
                                class="text-xs font-medium"
                                style="color: var(--muted-foreground);"
                              >
                                Processing Time
                              </div>
                            </div>
                          </div>
                        </div>
                      {/if}

                      <!-- Report Footer -->
                      <div
                        class="border-t-2 pt-4 text-center"
                        style="border-color: var(--border);"
                      >
                        <div
                          class="flex items-center justify-center gap-2 mb-2"
                        >
                          <Megaphone
                            class="w-4 h-4"
                            style="color: var(--primary);"
                          />
                          <span
                            class="font-bold"
                            style="color: var(--foreground);"
                            >Catalyst Intelligence Report</span
                          >
                        </div>
                        <p
                          class="text-xs"
                          style="color: var(--muted-foreground);"
                        >
                          Generated on {message.timestamp.toLocaleDateString()}
                          at {message.timestamp.toLocaleTimeString()}
                          • Powered by Apollo & Exa APIs
                        </p>
                      </div>
                    </div>
                  {:else}
                    <!-- Regular text content or debug info -->
                    <div class="prose prose-sm max-w-none">
                      {@html formatContent(message.content)}
                    </div>

                    <!-- Debug: Show raw data if available but not properly structured -->
                    {#if message.data && !message.data.targetCompany && !message.data.competitors}
                      <div
                        class="mt-4 p-3 border-2 rounded"
                        style="background: var(--muted); border-color: var(--border);"
                      >
                        <h4
                          class="font-bold mb-2"
                          style="color: var(--foreground);"
                        >
                          Debug: Raw Data
                        </h4>
                        <pre
                          class="text-xs overflow-auto"
                          style="color: var(--muted-foreground);">{JSON.stringify(
                            message.data,
                            null,
                            2,
                          )}</pre>
                      </div>
                    {/if}
                  {/if}
                </div>

                <!-- Follow-up Actions -->
                <div class="flex flex-wrap gap-2 mb-4">
                  <button
                    on:click={() => copyToClipboard(message.content)}
                    class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"
                    title="Copy research report to clipboard"
                  >
                    <Copy class="w-3 h-3" />
                    Copy Report
                  </button>
                  {#if message.isReport}
                    <button
                      on:click={() => downloadAsMarkdown(message)}
                      class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"
                      title="Download as Markdown file"
                    >
                      <Download class="w-3 h-3" />
                      Download
                    </button>
                  {/if}
                  <button
                    class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"
                    title="Export contact list"
                  >
                    <Users class="w-3 h-3" />
                    Export Contacts
                  </button>
                  <button
                    class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"
                    title="Share research findings"
                  >
                    <Mail class="w-3 h-3" />
                    Share Report
                  </button>
                </div>
              {/if}
            </div>
          </div>
        {/each}

        {#if isLoading}
          <div class="flex gap-4">
            <div
              class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2"
              style="background: var(--secondary); border-color: var(--border); box-shadow: var(--shadow-sm);"
            >
              <Bot
                class="w-5 h-5"
                style="color: var(--secondary-foreground);"
              />
            </div>
            <div class="flex-1">
              <div class="flex items-center gap-2 mb-2">
                <span
                  class="text-sm font-bold"
                  style="color: var(--foreground);">Catalyst</span
                >
                <span class="text-xs" style="color: var(--muted-foreground);"
                  >Analyzing company intelligence and competitive landscape...</span
                >
              </div>
              <div
                class="p-6 border-2"
                style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow-sm);"
              >
                <!-- Progress Timeline -->
                <div class="space-y-4">
                  {#each progressSteps as step (step.id)}
                    <div
                      class="flex items-start gap-3"
                      transition:slide={{ duration: 300 }}
                    >
                      <div class="flex-shrink-0 mt-0.5">
                        {#if step.status === "completed"}
                          <div transition:fade={{ duration: 200 }}>
                            <CheckCircle2
                              class="w-5 h-5 animate-scale-in"
                              style="color: var(--primary);"
                            />
                          </div>
                        {:else if step.status === "active"}
                          <Loader2
                            class="w-5 h-5 animate-spin"
                            style="color: var(--primary);"
                          />
                        {:else}
                          <Circle
                            class="w-5 h-5 opacity-30"
                            style="color: var(--muted-foreground);"
                          />
                        {/if}
                      </div>
                      <div class="flex-1">
                        <h4
                          class="text-sm font-bold mb-1"
                          style="color: {step.status === 'pending'
                            ? 'var(--muted-foreground)'
                            : 'var(--foreground)'};{step.status === 'pending'
                            ? 'opacity: 0.5'
                            : ''}"
                        >
                          {step.title}
                        </h4>
                        <p
                          class="text-xs"
                          style="color: var(--muted-foreground);{step.status ===
                          'pending'
                            ? 'opacity: 0.5'
                            : ''}"
                        >
                          {step.description}
                        </p>
                        {#if step.status === "active" && step.progress}
                          <div
                            class="mt-2 h-1 rounded-full overflow-hidden"
                            style="background: var(--muted);"
                          >
                            <div
                              class="h-full transition-all duration-500 ease-out"
                              style="background: var(--primary); width: {step.progress}%"
                            ></div>
                          </div>
                        {/if}
                      </div>
                    </div>
                  {/each}
                </div>

                <!-- Overall Progress -->
                <div
                  class="mt-6 pt-4 border-t"
                  style="border-color: var(--border);"
                >
                  <div class="flex items-center justify-between mb-2">
                    <span
                      class="text-xs font-medium"
                      style="color: var(--muted-foreground);"
                      >Overall Progress</span
                    >
                    <span
                      class="text-xs font-bold"
                      style="color: var(--foreground);">{currentProgress}%</span
                    >
                  </div>
                  <div
                    class="h-2 rounded-full overflow-hidden"
                    style="background: var(--muted);"
                  >
                    <div
                      class="h-full transition-all duration-500 ease-out"
                      style="background: linear-gradient(to right, var(--primary), var(--accent)); width: {currentProgress}%"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        {/if}
      </div>
    </div>
  </div>
</div>

<!-- Modern Floating Input Area -->
<div
  class="modern-input-area modern-input-area-seo {isLoading
    ? 'opacity-50 pointer-events-none'
    : ''}"
>
  <div class="max-w-4xl mx-auto">
    <div class="flex" style="gap: 15px;">
      <div class="flex-1 relative">
        <textarea
          bind:value={input}
          on:keydown={handleKeyDown}
          placeholder={currentPlaceholder}
          class="input-brutal enhanced-input flex-1 resize-none p-4 w-full h-full"
          style="min-height: 60px;"
          disabled={isLoading}
        ></textarea>
      </div>
      <button
        on:click={sendMessage}
        disabled={!input.trim() || isLoading}
        class="btn-primary px-6 font-bold flex items-center gap-2"
        style="height: auto; align-self: stretch;"
      >
        {#if isLoading}
          <div
            class="w-4 h-4 border-2 border-white/30 border-t-white animate-spin"
            style="border-radius: 50%;"
          ></div>
        {:else}
          <Megaphone class="w-4 h-4 animate-pulse" />
        {/if}
        Research
      </button>
    </div>
  </div>
</div>

<style>
  @keyframes scale-in {
    0% {
      transform: scale(0);
      opacity: 0;
    }
    50% {
      transform: scale(1.2);
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }
</style>
