<script lang="ts">
  import { onMount, onDestroy } from "svelte"
  import { page } from "$app/stores"
  import { goto } from "$app/navigation"
  import {
    PanelRightOpen,
    PanelRightClose,
    Plus,
    FileText,
    Search,
    MoreVertical,
    Trash2,
    ChevronRight,
    PenTool,
  } from "lucide-svelte"
  import ContentCanvas from "$lib/components/ContentCanvas.svelte"
  import ContentAgentSidebar from "$lib/components/ContentAgentSidebar.svelte"
  import PersistentNavigation from "$lib/components/PersistentNavigation.svelte"
  import { ContentService } from "$lib/services/content-service"
  import { navigationActions } from "$lib/stores/navigation"

  export let data

  let { session, supabase } = data
  let contentService = new ContentService(supabase)
  let environment: any = null

  // State
  let sidebarOpen = false
  let currentDocument: any = null
  let documents: any[] = []
  let isLoading = false
  let searchTerm = ""
  let selectedContentType = "all"
  let showDocumentList = true
  let selectedText = ""
  let canvasComponent: any

  // URL parameters for auto-outline mode
  let urlTopic = ""
  let urlContentType = ""
  let urlAudience = ""
  let autoGenerateOutline = false

  const contentTypes = [
    { value: "all", label: "All Types" },
    { value: "article", label: "Article" },
    { value: "blog-post", label: "Blog Post" },
    { value: "whitepaper", label: "Whitepaper" },
    { value: "document", label: "Document" },
    { value: "email", label: "Email" },
    { value: "social-media", label: "Social Media" },
    { value: "presentation", label: "Presentation" },
    { value: "report", label: "Report" },
    { value: "essay", label: "Essay" },
    { value: "tutorial", label: "Tutorial" },
    { value: "guide", label: "Guide" },
    { value: "proposal", label: "Proposal" },
  ]

  onMount(async () => {
    // Load environment first
    await loadEnvironment()

    // Check URL parameters for auto-outline mode
    const params = $page.url.searchParams
    urlTopic = params.get("topic") || ""
    urlContentType = params.get("type") || "article"
    urlAudience = params.get("audience") || "general"

    if (urlTopic && urlContentType && urlAudience) {
      autoGenerateOutline = true
      await createDocumentWithAutoOutline()
    } else {
      await loadDocuments()
    }
  })

  async function loadEnvironment() {
    try {
      const { data: env, error } = await supabase
        .from("environments")
        .select("*")
        .eq("slug", $page.params.envSlug)
        .single()

      if (error || !env) {
        console.error("Environment error:", error)
        goto("/dashboard")
        return
      }

      // Verify user has access to this environment
      const { data: access } = await supabase
        .from("environments_profiles")
        .select("*")
        .eq("environment_id", env.id)
        .eq("profile_id", session.user.id)
        .single()

      if (!access) {
        goto("/dashboard")
        return
      }

      environment = env
    } catch (error) {
      console.error("Error loading environment:", error)
      goto("/dashboard")
    }
  }

  async function createDocumentWithAutoOutline() {
    try {
      isLoading = true
      showDocumentList = false

      // Create a new document
      const newDoc = await contentService.createDocument({
        title: `${urlTopic} - ${urlContentType}`,
        content: "",
        content_type: urlContentType,
        target_audience: urlAudience,
        user_id: session.user.id,
        environment_id: environment.id,
      })

      if (newDoc) {
        currentDocument = newDoc

        // Open sidebar and trigger auto-outline generation
        sidebarOpen = true

        // Trigger the outline generation after a short delay to ensure sidebar is ready
        setTimeout(() => {
          // The sidebar will be opened and we can trigger the outline generation
          // This will be handled by the sidebar component's quick actions
        }, 500)
      }
    } catch (error) {
      console.error("Error creating document with auto-outline:", error)
    } finally {
      isLoading = false
    }
  }

  async function loadDocuments() {
    try {
      isLoading = true
      documents = await contentService.getUserDocuments(
        session.user.id,
        environment.id,
        {
          limit: 50,
          contentType:
            selectedContentType === "all" ? undefined : selectedContentType,
        },
      )
    } catch (error) {
      console.error("Error loading documents:", error)
    } finally {
      isLoading = false
    }
  }

  async function createNewDocument() {
    try {
      if (!environment) {
        console.error("Environment not loaded")
        return
      }

      if (!session?.user?.id) {
        console.error("User session not available")
        return
      }

      const newDoc = await contentService.createDocument({
        title: "Untitled Document",
        content: "",
        content_type: "article",
        target_audience: "general",
        user_id: session.user.id,
        environment_id: environment.id,
      })

      if (newDoc) {
        currentDocument = newDoc
        showDocumentList = false
      }
    } catch (error) {
      console.error("Error creating document:", error)
      alert(`Failed to create document: ${error.message}`)
    }
  }

  async function openDocument(doc: any) {
    currentDocument = doc
    showDocumentList = false
  }

  async function saveDocument(event: CustomEvent) {
    if (!currentDocument) return

    try {
      const { content, title } = event.detail

      // Calculate word count and reading time
      const plainText = content
        .replace(/<[^>]*>/g, " ")
        .replace(/\s+/g, " ")
        .trim()
      const wordCount = plainText
        .split(" ")
        .filter((word) => word.length > 0).length
      const readingTime = Math.ceil(wordCount / 200) // Average reading speed

      await contentService.updateDocument(currentDocument.id, session.user.id, {
        title,
        content: content, // Save as plain text/HTML
        word_count: wordCount,
        reading_time: readingTime,
        updated_at: new Date().toISOString(),
      })

      // Update local document
      currentDocument = {
        ...currentDocument,
        title,
        content: content,
        word_count: wordCount,
        reading_time: readingTime,
      }
    } catch (error) {
      console.error("Error saving document:", error)
      throw error
    }
  }

  async function deleteDocument(docId: string) {
    if (!confirm("Are you sure you want to delete this document?")) return

    try {
      await contentService.deleteDocument(docId, session.user.id)
      await loadDocuments()

      if (currentDocument?.id === docId) {
        currentDocument = null
        showDocumentList = true
      }
    } catch (error) {
      console.error("Error deleting document:", error)
    }
  }

  function toggleSidebar() {
    sidebarOpen = !sidebarOpen

    // Update navigation state
    if (sidebarOpen) {
      navigationActions.setAgentActive('nexus')
    } else {
      navigationActions.setAgentInactive()
    }
  }

  function handleSelectionChange(event: CustomEvent) {
    selectedText = event.detail.selectedText || ""
  }

  function handleReplaceContent(event: CustomEvent) {
    const { content, replaceSelected } = event.detail

    if (canvasComponent) {
      if (replaceSelected && selectedText.trim()) {
        canvasComponent.replaceSelectedContent(content)
      } else {
        canvasComponent.replaceEntireContent(content)
      }
    }
  }

  onMount(() => {
    // Set up navigation state when component mounts
    if (sidebarOpen) {
      navigationActions.setAgentActive('nexus')
    }
  })

  onDestroy(() => {
    // Clean up navigation state when component unmounts
    navigationActions.setAgentInactive()
  })

  function backToDocuments() {
    currentDocument = null
    showDocumentList = true

    // Clear URL parameters if they exist
    if (urlTopic || urlContentType || urlAudience) {
      goto(`/dashboard/${$page.params.envSlug}/content-agent`, {
        replaceState: true,
      })
    }
  }

  function handleContentGenerated(event: CustomEvent) {
    const { content, action } = event.detail

    if (currentDocument && content) {
      // Ensure content is properly formatted for HTML display
      let formattedContent = content

      // If content is plain text, convert line breaks to HTML
      if (typeof content === "string" && !content.includes("<")) {
        formattedContent = content
          .split("\n")
          .map((line) => line.trim())
          .filter((line) => line.length > 0)
          .map((line) => `<p>${line}</p>`)
          .join("")
      }

      // Update the document content with the generated content
      currentDocument.content = formattedContent

      // Force reactivity update
      currentDocument = { ...currentDocument }

      // Auto-save the generated content
      saveDocument({
        detail: {
          content: formattedContent,
          title: currentDocument.title,
        },
      } as CustomEvent)
    }
  }

  function filteredDocuments() {
    let filtered = documents

    if (searchTerm) {
      filtered = filtered.filter(
        (doc) =>
          doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          doc.content?.text?.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    return filtered
  }

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  function getContentTypeLabel(type: string) {
    const found = contentTypes.find((ct) => ct.value === type)
    return found ? found.label : type
  }
</script>

<svelte:head>
  <title
    >Nexus - AI Content Assistant - {environment?.name || "Loading..."}</title
  >
</svelte:head>

<div class="h-screen flex flex-col" style="background: var(--background);">
  <!-- Header -->
  <div
    class="border-b-2 flex-shrink-0"
    style="border-color: var(--border); background: var(--background);"
  >
    <div class="max-w-7xl mx-auto px-6 lg:px-8 py-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div
            class="w-12 h-12 flex items-center justify-center border-2 hover:scale-105 transition-transform cursor-pointer"
            style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"
          >
            <PenTool
              class="w-6 h-6"
              style="color: var(--primary-foreground);"
            />
          </div>
          <div>
            <h1 class="text-3xl font-black" style="color: var(--foreground);">
              Nexus
            </h1>
            <p
              class="text-lg font-medium"
              style="color: var(--muted-foreground);"
            >
              Your AI content creator
            </p>
          </div>
          {#if autoGenerateOutline}
            <div
              class="px-3 py-1 bg-primary/10 text-primary text-sm"
              style="border-radius: var(--radius);"
            >
              Auto-generating outline for: {urlTopic}
            </div>
          {/if}
        </div>
        <div class="flex items-center space-x-4">
          {#if !showDocumentList}
            <button
              on:click={backToDocuments}
              class="flex items-center gap-2 px-4 py-2 text-sm border-2 hover:scale-105 transition-transform"
              style="background: var(--secondary); border-color: var(--border); color: var(--secondary-foreground); box-shadow: var(--shadow-sm);"
            >
              ← Back to Documents
            </button>
            <button
              on:click={toggleSidebar}
              class="flex items-center gap-2 px-4 py-2 border-2 hover:scale-105 transition-transform"
              style="background: var(--primary); border-color: var(--border); color: var(--primary-foreground); box-shadow: var(--shadow-sm);"
            >
              {#if sidebarOpen}
                <PanelRightClose class="w-4 h-4" />
                Close Agent
              {:else}
                <PanelRightOpen class="w-4 h-4" />
                Open Agent
              {/if}
            </button>
          {/if}
        </div>
      </div>
    </div>
  </div>

  <!-- Breadcrumb Navigation -->
  <div class="max-w-7xl mx-auto px-6 lg:px-8 py-4">
    <nav class="flex items-center space-x-2 text-sm text-muted-foreground">
      <a
        href="/dashboard/{$page.params.envSlug}"
        class="hover:text-foreground transition-colors"
      >
        Dashboard
      </a>
      <ChevronRight class="w-4 h-4" />
      <span class="text-foreground font-medium">Nexus</span>
    </nav>
  </div>

  <!-- Main Content -->
  <div
    class="flex-1 overflow-hidden max-w-7xl mx-auto px-6 lg:px-8 py-8 w-full"
  >
    {#if showDocumentList}
      <!-- Document List View -->
      <div class="h-full">
        <!-- Controls -->
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center gap-4">
            <div class="relative">
              <Search
                class="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"
              />
              <input
                bind:value={searchTerm}
                placeholder="Search documents..."
                class="pl-10 pr-4 py-2 border border-border bg-background focus:outline-none focus:ring-2 focus:ring-primary"
                style="border-radius: var(--radius);"
              />
            </div>

            <select
              bind:value={selectedContentType}
              on:change={loadDocuments}
              class="px-3 py-2 border border-border bg-background focus:outline-none focus:ring-2 focus:ring-primary"
              style="border-radius: var(--radius);"
            >
              {#each contentTypes as type}
                <option value={type.value}>{type.label}</option>
              {/each}
            </select>
          </div>

          <button
            on:click={createNewDocument}
            class="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground hover:bg-primary/90 transition-colors"
            style="border-radius: var(--radius);"
          >
            <Plus class="w-4 h-4" />
            New Document
          </button>
        </div>

        <!-- Documents Grid -->
        {#if isLoading}
          <div class="flex items-center justify-center py-12">
            <div
              class="animate-spin h-8 w-8 border-b-2 border-primary"
              style="border-radius: 50%;"
            ></div>
          </div>
        {:else if filteredDocuments().length === 0}
          <div class="text-center py-12">
            <FileText class="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
            <h3 class="text-lg font-medium mb-2">No documents found</h3>
            <p class="text-muted-foreground mb-4">
              {searchTerm
                ? "Try adjusting your search terms"
                : "Create your first document to get started"}
            </p>
            {#if !searchTerm}
              <button
                on:click={createNewDocument}
                class="px-4 py-2 bg-primary text-primary-foreground hover:bg-primary/90 transition-colors"
                style="border-radius: var(--radius);"
              >
                Create Document
              </button>
            {/if}
          </div>
        {:else}
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {#each filteredDocuments() as doc}
              <div
                class="border border-border p-4 hover:shadow-md transition-shadow bg-card"
                style="border-radius: var(--radius); box-shadow: var(--shadow-xs);"
              >
                <div class="flex items-start justify-between mb-3">
                  <h3 class="font-medium truncate flex-1 mr-2">{doc.title}</h3>
                  <div class="relative">
                    <button class="p-1 hover:bg-muted rounded">
                      <MoreVertical class="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <div
                  class="flex items-center gap-2 text-sm text-muted-foreground mb-3"
                >
                  <span class="px-2 py-1 bg-muted rounded text-xs">
                    {getContentTypeLabel(doc.content_type)}
                  </span>
                  <span>•</span>
                  <span>{formatDate(doc.updated_at)}</span>
                </div>

                <div class="flex items-center justify-between">
                  <button
                    on:click={() => openDocument(doc)}
                    class="text-primary hover:text-primary/80 text-sm font-medium"
                  >
                    Open →
                  </button>

                  <button
                    on:click={() => deleteDocument(doc.id)}
                    class="p-1 text-muted-foreground hover:text-destructive rounded"
                  >
                    <Trash2 class="w-4 h-4" />
                  </button>
                </div>
              </div>
            {/each}
          </div>
        {/if}
      </div>
    {:else}
      <!-- Document Editor View -->
      <div class="h-full flex">
        <div class="flex-1 flex flex-col">
          <ContentCanvas
            bind:this={canvasComponent}
            content={currentDocument?.content || ""}
            title={currentDocument?.title || ""}
            on:save={saveDocument}
            on:contentChange={(e) => {
              if (currentDocument) {
                currentDocument.content = e.detail.content
              }
            }}
            on:titleChange={(e) => {
              if (currentDocument) {
                currentDocument.title = e.detail.title
              }
            }}
            on:selectionChange={handleSelectionChange}
            {isLoading}
          />
        </div>

        <!-- Agent Sidebar -->
        <ContentAgentSidebar
          bind:isOpen={sidebarOpen}
          documentId={currentDocument?.id}
          currentContent={currentDocument?.content || ""}
          {selectedText}
          envSlug={$page.params.envSlug}
          on:close={() => {
            sidebarOpen = false
            navigationActions.setAgentInactive()
          }}
          on:contentGenerated={handleContentGenerated}
          on:replaceContent={handleReplaceContent}
        />
      </div>
    {/if}
  </div>
</div>

<!-- Persistent Navigation -->
<PersistentNavigation envSlug={$page.params.envSlug} />
