import { writable } from 'svelte/store'

export interface NavigationItem {
  id: string
  label: string
  href: string
  icon?: string
  active?: boolean
  description?: string
}

export interface NavigationState {
  isAgentActive: boolean
  activeAgentId: string | null
  showPersistentNav: boolean
  items: NavigationItem[]
}

const defaultNavigationItems: NavigationItem[] = [
  {
    id: 'compass',
    label: 'Compass',
    href: '/researcher',
    description: 'Company Research Agent',
    icon: '🧭'
  },
  {
    id: 'keystone', 
    label: 'Keystone',
    href: '/agent-seo',
    description: 'SEO Agent',
    icon: '🔍'
  },
  {
    id: 'nexus',
    label: 'Nexus',
    href: '/content-agent',
    description: 'Content Agent',
    icon: '✍️'
  },
  {
    id: 'catalyst',
    label: 'Catalyst',
    href: '/campaign-orchestrator',
    description: 'Campaign Orchestrator',
    icon: '📢'
  }
]

const initialState: NavigationState = {
  isAgentActive: false,
  activeAgentId: null,
  showPersistentNav: false,
  items: defaultNavigationItems
}

export const navigationState = writable<NavigationState>(initialState)

// Helper functions
export const navigationActions = {
  setAgentActive: (agentId: string) => {
    navigationState.update(state => ({
      ...state,
      isAgentActive: true,
      activeAgentId: agentId,
      showPersistentNav: true
    }))
  },

  setAgentInactive: () => {
    navigationState.update(state => ({
      ...state,
      isAgentActive: false,
      activeAgentId: null,
      showPersistentNav: false
    }))
  },

  togglePersistentNav: () => {
    navigationState.update(state => ({
      ...state,
      showPersistentNav: !state.showPersistentNav
    }))
  },

  updateActiveItem: (currentPath: string, envSlug: string) => {
    navigationState.update(state => ({
      ...state,
      items: state.items.map(item => ({
        ...item,
        active: currentPath.includes(item.href),
        href: `/dashboard/${envSlug}${item.href}`
      }))
    }))
  }
}
