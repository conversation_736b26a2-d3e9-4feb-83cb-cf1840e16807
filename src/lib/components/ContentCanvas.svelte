<script lang="ts">
  import { onMount, createEventDispatcher } from "svelte"
  import {
    Bold,
    Italic,
    Underline,
    List,
    ListOrdered,
    Quote,
    Heading1,
    Heading2,
    Heading3,
    Undo,
    Redo,
    Save,
    FileText,
  } from "lucide-svelte"
  import { debounce } from "$lib/utils"

  export let content: string = ""
  export let title: string = "Untitled Document"
  export let isLoading: boolean = false
  export let autoSave: boolean = true
  export let placeholder: string = "Start writing your content..."

  const dispatch = createEventDispatcher()

  let editor: HTMLDivElement
  let titleInput: HTMLInputElement
  let wordCount = 0
  let characterCount = 0
  let lastSaved = new Date()
  let isSaving = false
  let hasUnsavedChanges = false
  let selectedText = ""

  // Debounced save function
  const debouncedSave = debounce(() => {
    if (autoSave && hasUnsavedChanges) {
      saveContent()
    }
  }, 2000)

  // Debounced content analysis
  const debouncedAnalyze = debounce(() => {
    analyzeContent()
  }, 500)

  onMount(() => {
    if (editor) {
      editor.innerHTML = content
      analyzeContent()
    }
  })

  // Reactive statement to update editor when content prop changes
  $: if (editor && content !== editor.innerHTML) {
    editor.innerHTML = content
    analyzeContent()
  }

  function formatText(command: string, value?: string) {
    document.execCommand(command, false, value)
    editor.focus()
    handleContentChange()
  }

  function handleContentChange() {
    if (editor) {
      content = editor.innerHTML
      hasUnsavedChanges = true
      debouncedAnalyze()
      debouncedSave()
      dispatch("contentChange", { content, title })
    }
  }

  function getSelectedText(): string {
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      return selection.toString().trim()
    }
    return ""
  }

  function handleSelectionChange() {
    const newSelectedText = getSelectedText()
    if (newSelectedText !== selectedText) {
      selectedText = newSelectedText
      dispatch("selectionChange", {
        selectedText,
        hasSelection: selectedText.length > 0,
        fullContent: editor?.innerText || ""
      })
    }
  }

  function replaceSelectedContent(newContent: string) {
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0 && selectedText) {
      const range = selection.getRangeAt(0)
      range.deleteContents()

      // Create a temporary div to parse HTML content
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = newContent

      // Insert the parsed content
      const fragment = document.createDocumentFragment()
      while (tempDiv.firstChild) {
        fragment.appendChild(tempDiv.firstChild)
      }
      range.insertNode(fragment)

      // Clear selection
      selection.removeAllRanges()
      selectedText = ""

      handleContentChange()
    } else {
      // If no selection, replace entire content
      if (editor) {
        editor.innerHTML = newContent
        handleContentChange()
      }
    }
  }

  function replaceEntireContent(newContent: string) {
    if (editor) {
      editor.innerHTML = newContent
      handleContentChange()
    }
  }

  // Export functions for parent component access
  export { getSelectedText, replaceSelectedContent, replaceEntireContent }

  function handleTitleChange() {
    hasUnsavedChanges = true
    debouncedSave()
    dispatch("titleChange", { title })
  }

  function analyzeContent() {
    if (editor) {
      const text = editor.innerText || ""
      wordCount = text.trim() ? text.trim().split(/\s+/).length : 0
      characterCount = text.length
    }
  }

  async function saveContent() {
    if (isSaving) return

    isSaving = true
    try {
      await dispatch("save", { content, title })
      hasUnsavedChanges = false
      lastSaved = new Date()
    } catch (error) {
      console.error("Save failed:", error)
    } finally {
      isSaving = false
    }
  }

  function insertHeading(level: number) {
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      const heading = document.createElement(`h${level}`)
      heading.textContent = selection.toString() || `Heading ${level}`

      range.deleteContents()
      range.insertNode(heading)

      // Move cursor after heading
      range.setStartAfter(heading)
      range.collapse(true)
      selection.removeAllRanges()
      selection.addRange(range)
    }
    handleContentChange()
  }

  function insertList(ordered: boolean = false) {
    const listType = ordered ? "insertOrderedList" : "insertUnorderedList"
    formatText(listType)
  }

  function insertBlockquote() {
    formatText("formatBlock", "blockquote")
  }

  function handleKeyDown(event: KeyboardEvent) {
    // Handle keyboard shortcuts
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case "s":
          event.preventDefault()
          saveContent()
          break
        case "b":
          event.preventDefault()
          formatText("bold")
          break
        case "i":
          event.preventDefault()
          formatText("italic")
          break
        case "u":
          event.preventDefault()
          formatText("underline")
          break
        case "z":
          event.preventDefault()
          if (event.shiftKey) {
            formatText("redo")
          } else {
            formatText("undo")
          }
          break
      }
    }
  }

  function getReadingTime(): string {
    const wordsPerMinute = 200
    const minutes = Math.ceil(wordCount / wordsPerMinute)
    return `${minutes} min read`
  }

  function formatLastSaved(): string {
    const now = new Date()
    const diff = now.getTime() - lastSaved.getTime()
    const minutes = Math.floor(diff / 60000)

    if (minutes < 1) return "Just now"
    if (minutes < 60) return `${minutes}m ago`

    const hours = Math.floor(minutes / 60)
    if (hours < 24) return `${hours}h ago`

    return lastSaved.toLocaleDateString()
  }
</script>

<!-- Toolbar -->
<div class="border-b border-border bg-card p-4">
  <!-- Title Input -->
  <div class="mb-4">
    <input
      bind:this={titleInput}
      bind:value={title}
      on:input={handleTitleChange}
      class="w-full text-2xl font-bold bg-transparent border-none outline-none placeholder-muted-foreground"
      placeholder="Document title..."
      disabled={isLoading}
    />
  </div>

  <!-- Formatting Toolbar -->
  <div class="flex items-center gap-2 flex-wrap">
    <!-- Text Formatting -->
    <div class="flex items-center gap-1 border-r border-border pr-2">
      <button
        type="button"
        on:click={() => formatText("bold")}
        class="p-2 hover:bg-muted rounded transition-colors"
        title="Bold (Ctrl+B)"
        disabled={isLoading}
      >
        <Bold class="w-4 h-4" />
      </button>
      <button
        type="button"
        on:click={() => formatText("italic")}
        class="p-2 hover:bg-muted rounded transition-colors"
        title="Italic (Ctrl+I)"
        disabled={isLoading}
      >
        <Italic class="w-4 h-4" />
      </button>
      <button
        type="button"
        on:click={() => formatText("underline")}
        class="p-2 hover:bg-muted rounded transition-colors"
        title="Underline (Ctrl+U)"
        disabled={isLoading}
      >
        <Underline class="w-4 h-4" />
      </button>
    </div>

    <!-- Headings -->
    <div class="flex items-center gap-1 border-r border-border pr-2">
      <button
        type="button"
        on:click={() => insertHeading(1)}
        class="p-2 hover:bg-muted rounded transition-colors"
        title="Heading 1"
        disabled={isLoading}
      >
        <Heading1 class="w-4 h-4" />
      </button>
      <button
        type="button"
        on:click={() => insertHeading(2)}
        class="p-2 hover:bg-muted rounded transition-colors"
        title="Heading 2"
        disabled={isLoading}
      >
        <Heading2 class="w-4 h-4" />
      </button>
      <button
        type="button"
        on:click={() => insertHeading(3)}
        class="p-2 hover:bg-muted rounded transition-colors"
        title="Heading 3"
        disabled={isLoading}
      >
        <Heading3 class="w-4 h-4" />
      </button>
    </div>

    <!-- Lists and Quote -->
    <div class="flex items-center gap-1 border-r border-border pr-2">
      <button
        type="button"
        on:click={() => insertList(false)}
        class="p-2 hover:bg-muted rounded transition-colors"
        title="Bullet List"
        disabled={isLoading}
      >
        <List class="w-4 h-4" />
      </button>
      <button
        type="button"
        on:click={() => insertList(true)}
        class="p-2 hover:bg-muted rounded transition-colors"
        title="Numbered List"
        disabled={isLoading}
      >
        <ListOrdered class="w-4 h-4" />
      </button>
      <button
        type="button"
        on:click={insertBlockquote}
        class="p-2 hover:bg-muted rounded transition-colors"
        title="Quote"
        disabled={isLoading}
      >
        <Quote class="w-4 h-4" />
      </button>
    </div>

    <!-- Undo/Redo -->
    <div class="flex items-center gap-1 border-r border-border pr-2">
      <button
        type="button"
        on:click={() => formatText("undo")}
        class="p-2 hover:bg-muted rounded transition-colors"
        title="Undo (Ctrl+Z)"
        disabled={isLoading}
      >
        <Undo class="w-4 h-4" />
      </button>
      <button
        type="button"
        on:click={() => formatText("redo")}
        class="p-2 hover:bg-muted rounded transition-colors"
        title="Redo (Ctrl+Shift+Z)"
        disabled={isLoading}
      >
        <Redo class="w-4 h-4" />
      </button>
    </div>

    <!-- Save Button -->
    <button
      type="button"
      on:click={saveContent}
      class="flex items-center gap-2 px-3 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90 transition-colors"
      disabled={isLoading || isSaving || !hasUnsavedChanges}
      title="Save (Ctrl+S)"
    >
      <Save class="w-4 h-4" />
      {#if isSaving}
        Saving...
      {:else if hasUnsavedChanges}
        Save
      {:else}
        Saved
      {/if}
    </button>
  </div>

  <!-- Status Bar -->
  <div
    class="flex items-center justify-between mt-3 text-sm text-muted-foreground"
  >
    <div class="flex items-center gap-4">
      <span>{wordCount} words</span>
      <span>{characterCount} characters</span>
      <span>{getReadingTime()}</span>
    </div>
    <div class="flex items-center gap-2">
      {#if hasUnsavedChanges}
        <span class="text-orange-500">Unsaved changes</span>
      {:else}
        <span>Last saved: {formatLastSaved()}</span>
      {/if}
    </div>
  </div>
</div>

<!-- Editor -->
<div class="flex-1 p-6 overflow-y-auto">
  <div
    bind:this={editor}
    contenteditable="true"
    on:input={handleContentChange}
    on:keydown={handleKeyDown}
    on:mouseup={handleSelectionChange}
    on:keyup={handleSelectionChange}
    class="min-h-full outline-none prose prose-slate max-w-none
           prose-headings:text-foreground prose-p:text-foreground
           prose-strong:text-foreground prose-em:text-foreground
           prose-blockquote:text-muted-foreground prose-blockquote:border-border
           prose-ul:text-foreground prose-ol:text-foreground prose-li:text-foreground
           focus:outline-none"
    data-placeholder={placeholder}
    class:empty={!content}
    disabled={isLoading}
  ></div>
</div>

<style>
  [contenteditable]:empty:before {
    content: attr(data-placeholder);
    color: hsl(var(--muted-foreground));
    pointer-events: none;
  }

  .prose blockquote {
    border-left: 4px solid hsl(var(--border));
    padding-left: 1rem;
    margin-left: 0;
    font-style: italic;
  }

  .prose h1,
  .prose h2,
  .prose h3 {
    margin-top: 2rem;
    margin-bottom: 1rem;
  }

  .prose p {
    margin-bottom: 1rem;
  }

  .prose ul,
  .prose ol {
    margin-bottom: 1rem;
  }
</style>
