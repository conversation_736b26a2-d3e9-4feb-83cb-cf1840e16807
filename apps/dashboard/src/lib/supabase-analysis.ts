import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY } from '$env/static/public';

// Create Supabase client for dashboard (read-only operations)
const supabase = createClient(PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY);

export interface AnalysisSession {
  id: string;
  user_id: string;
  url: string;
  company_name?: string;
  industry?: string;
  competitors?: string[];
  prompts?: string[];
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  progress_stage: string;
  progress_percentage: number;
  progress_message: string;
  result_data?: any;
  error_message?: string;
  created_at: string;
  updated_at: string;
}

export async function getAnalysisSession(sessionId: string): Promise<AnalysisSession | null> {
  const { data, error } = await supabase
    .from('brand_analysis_sessions')
    .select('*')
    .eq('id', sessionId)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      // No rows returned
      return null;
    }
    console.error('Failed to get analysis session:', error);
    return null;
  }

  return data;
}

export async function getUserAnalysisSessions(userId: string): Promise<AnalysisSession[]> {
  const { data, error } = await supabase
    .from('brand_analysis_sessions')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Failed to get user analysis sessions:', error);
    return [];
  }

  return data || [];
}

// Subscribe to real-time changes for a specific analysis session
export function subscribeToAnalysisSession(
  sessionId: string,
  callback: (session: AnalysisSession) => void
) {
  const subscription = supabase
    .channel(`analysis_session_${sessionId}`)
    .on(
      'postgres_changes',
      {
        event: 'UPDATE',
        schema: 'public',
        table: 'brand_analysis_sessions',
        filter: `id=eq.${sessionId}`
      },
      (payload) => {
        console.log('Analysis session updated:', payload.new);
        callback(payload.new as AnalysisSession);
      }
    )
    .subscribe();

  return subscription;
}

// Subscribe to real-time changes for all user's analysis sessions
export function subscribeToUserAnalysisSessions(
  userId: string,
  callback: (session: AnalysisSession) => void
) {
  const subscription = supabase
    .channel(`user_analysis_sessions_${userId}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'brand_analysis_sessions',
        filter: `user_id=eq.${userId}`
      },
      (payload) => {
        console.log('User analysis session changed:', payload);
        if (payload.new) {
          callback(payload.new as AnalysisSession);
        }
      }
    )
    .subscribe();

  return subscription;
}
