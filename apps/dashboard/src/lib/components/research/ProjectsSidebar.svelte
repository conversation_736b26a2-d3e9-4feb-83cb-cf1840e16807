<script lang="ts">
  import { Grid3X3, <PERSON>u, X, Plus, Clock } from "lucide-svelte"

  // Props for collapsible behavior
  export let collapsed = false
  export let isMobile = false
  export let projects = []

  // Toggle function
  function toggleSidebar() {
    collapsed = !collapsed
  }

  // Default projects data (placeholder)
  const defaultProjects = [
    {
      id: "1",
      title: "Generate 5 attention-grab...",
      description: '"Revolutionize Customer Enga...',
    },
    {
      id: "2",
      title: "Learning From 100 Years o...",
      description: "For athletes, high altitude prod...",
    },
    {
      id: "3",
      title: "Research officiants",
      description: "<PERSON>'s equations—the foun...",
    },
    {
      id: "4",
      title: "What does a senior lead de...",
      description: "Physiological respiration involv...",
    },
    {
      id: "5",
      title: "Write a sweet note to your...",
      description: "In the eighteenth century the G...",
    },
    {
      id: "6",
      title: "Meet with cake bakers",
      description: "Physical space is often conceiv...",
    },
    {
      id: "7",
      title: "Meet with cake bakers",
      description: "Physical space is often conceiv...",
    },
  ]

  // Use provided projects or default ones
  $: displayProjects = projects.length > 0 ? projects : defaultProjects
</script>

<!-- Right Sidebar - Projects -->
<aside
  class="bg-background border-l border-border overflow-y-auto transition-all duration-300 ease-in-out relative"
  class:w-80={!collapsed}
  class:w-16={collapsed && !isMobile}
  class:w-0={collapsed && isMobile}
  class:overflow-hidden={collapsed && isMobile}
>
  <!-- Toggle Button -->
  <button
    on:click={toggleSidebar}
    class="absolute -left-3 top-6 z-10 w-6 h-6 bg-background border border-border rounded-full flex items-center justify-center hover:bg-accent transition-colors"
    class:hidden={isMobile && collapsed}
  >
    {#if collapsed}
      <Menu class="w-3 h-3" />
    {:else}
      <X class="w-3 h-3" />
    {/if}
  </button>

  <div class="p-6" class:hidden={collapsed && isMobile}>
    <div class="flex items-center justify-between mb-6">
      {#if !collapsed}
        <h2 class="text-lg font-semibold text-foreground">
          Projects ({displayProjects.length})
        </h2>
        <button
          class="text-muted-foreground hover:text-foreground transition-colors"
        >
          <Grid3X3 class="w-5 h-5" />
        </button>
      {:else}
        <!-- Collapsed header -->
        <div class="flex flex-col items-center w-full">
          <button class="p-2 hover:bg-accent rounded-lg mb-2" title="Projects">
            <Grid3X3 class="w-5 h-5" />
          </button>
          <span class="text-xs text-muted-foreground"
            >{displayProjects.length}</span
          >
        </div>
      {/if}
    </div>

    {#if !collapsed}
      <div class="space-y-4">
        {#each displayProjects as project (project.id)}
          <div
            class="p-4 border border-border rounded-lg hover:bg-accent cursor-pointer transition-colors group"
          >
            <h3
              class="font-medium text-sm mb-2 text-foreground group-hover:text-accent-foreground"
            >
              {project.title}
            </h3>
            <p
              class="text-xs text-muted-foreground group-hover:text-accent-foreground/80"
            >
              {project.description}
            </p>
          </div>
        {/each}
      </div>
    {:else}
      <!-- Collapsed projects - show as dots -->
      <div class="flex flex-col items-center space-y-2">
        {#each displayProjects.slice(0, 5) as project (project.id)}
          <button
            class="w-2 h-2 bg-muted-foreground rounded-full hover:bg-foreground transition-colors"
            title={project.title}
            aria-label={project.title}
          ></button>
        {/each}
        {#if displayProjects.length > 5}
          <span class="text-xs text-muted-foreground"
            >+{displayProjects.length - 5}</span
          >
        {/if}
      </div>
    {/if}

    <!-- Quick Start Templates Section -->
    <div class="mt-8">
      {#if !collapsed}
        <h3 class="text-md font-semibold text-foreground mb-4">
          Quick Start Templates
        </h3>
        <div class="space-y-3">
          <button
            class="w-full p-3 text-left border border-border rounded-lg hover:bg-accent transition-colors group"
          >
            <div
              class="text-sm font-medium text-foreground group-hover:text-accent-foreground"
            >
              Competitive Analysis
            </div>
            <div
              class="text-xs text-muted-foreground group-hover:text-accent-foreground/80 mt-1"
            >
              Analyze competitors' strategies and positioning
            </div>
          </button>

          <button
            class="w-full p-3 text-left border border-border rounded-lg hover:bg-accent transition-colors group"
          >
            <div
              class="text-sm font-medium text-foreground group-hover:text-accent-foreground"
            >
              Market Research
            </div>
            <div
              class="text-xs text-muted-foreground group-hover:text-accent-foreground/80 mt-1"
            >
              Research market trends and opportunities
            </div>
          </button>

          <button
            class="w-full p-3 text-left border border-border rounded-lg hover:bg-accent transition-colors group"
          >
            <div
              class="text-sm font-medium text-foreground group-hover:text-accent-foreground"
            >
              Industry Analysis
            </div>
            <div
              class="text-xs text-muted-foreground group-hover:text-accent-foreground/80 mt-1"
            >
              Deep dive into industry dynamics
            </div>
          </button>

          <button
            class="w-full p-3 text-left border border-border rounded-lg hover:bg-accent transition-colors group"
          >
            <div
              class="text-sm font-medium text-foreground group-hover:text-accent-foreground"
            >
              Customer Research
            </div>
            <div
              class="text-xs text-muted-foreground group-hover:text-accent-foreground/80 mt-1"
            >
              Understand customer needs and behavior
            </div>
          </button>
        </div>
      {:else}
        <!-- Collapsed templates - show as plus icon -->
        <div class="flex justify-center">
          <button
            class="p-2 hover:bg-accent rounded-lg"
            title="Quick Start Templates"
          >
            <Plus class="w-4 h-4" />
          </button>
        </div>
      {/if}
    </div>
  </div>
</aside>
