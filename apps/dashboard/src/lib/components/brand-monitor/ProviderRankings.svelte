<script lang="ts">
  export let providerRankings: any[] = []
  export let brandName: string = ""
  export let shareOfVoice: number | undefined = undefined
  export let averagePosition: number | undefined = undefined
  export let sentimentScore: number | undefined = undefined
  export let weeklyChange: number | undefined = undefined

  let selectedProvider = providerRankings?.[0]?.provider || "OpenAI"

  $: currentRanking = providerRankings.find(
    (r) => r.provider === selectedProvider,
  )

  function getProviderIcon(provider: string) {
    switch (provider) {
      case "OpenAI":
        return "🤖"
      case "Anthropic":
        return "🧠"
      case "Google":
        return "🔍"
      case "Perplexity":
        return "🔮"
      default:
        return "🤖"
    }
  }

  function getSentimentBadge(sentiment: string) {
    switch (sentiment) {
      case "positive":
        return { text: "Positive", class: "bg-green-100 text-green-800" }
      case "negative":
        return { text: "Negative", class: "bg-red-100 text-red-800" }
      default:
        return { text: "Neutral", class: "bg-gray-100 text-gray-800" }
    }
  }

  function getTrendIcon(change: number | undefined) {
    if (change === undefined) return ""
    if (change > 0) return "📈"
    if (change < 0) return "📉"
    return "➖"
  }

  function getTrendClass(change: number | undefined) {
    if (change === undefined) return "text-muted-foreground"
    if (change > 0) return "text-green-600"
    if (change < 0) return "text-red-600"
    return "text-muted-foreground"
  }
</script>

{#if !providerRankings || providerRankings.length === 0}
  <div class="text-center py-12 bg-muted/50 rounded-lg">
    <p class="text-muted-foreground text-lg mb-2">
      No provider rankings available
    </p>
    <p class="text-muted-foreground text-sm">
      The analysis may still be processing or no provider data was returned.
    </p>
  </div>
{:else}
  <div class="space-y-6">
    <!-- Provider Tabs -->
    <div class="border-b border-border">
      <div
        class="flex space-x-1 {providerRankings.length === 2
          ? 'grid-cols-2'
          : providerRankings.length === 3
            ? 'grid-cols-3'
            : 'grid-cols-4'}"
      >
        {#each providerRankings as { provider }}
          <button
            onclick={() => (selectedProvider = provider)}
            class="flex items-center justify-center gap-2 px-4 py-3 text-sm font-medium rounded-t-lg transition-colors {selectedProvider ===
            provider
              ? 'bg-orange-100 text-orange-600 border-b-2 border-orange-600'
              : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'}"
          >
            <span class="text-lg">{getProviderIcon(provider)}</span>
            <span>{provider}</span>
          </button>
        {/each}
      </div>
    </div>

    <!-- Provider Content -->
    {#if currentRanking}
      <div class="space-y-6">
        <!-- Summary Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          {#if shareOfVoice !== undefined}
            <div class="bg-muted/50 p-4 rounded-lg border border-border">
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-sm font-medium text-foreground">
                    Share of Voice
                  </div>
                  <div class="text-2xl font-bold text-orange-600">
                    {shareOfVoice.toFixed(1)}%
                  </div>
                </div>
                {#if weeklyChange !== undefined}
                  <div class="text-right">
                    <span class="text-lg">{getTrendIcon(weeklyChange)}</span>
                    <div class="text-xs {getTrendClass(weeklyChange)}">
                      {weeklyChange > 0 ? "+" : ""}{weeklyChange.toFixed(1)}%
                    </div>
                  </div>
                {/if}
              </div>
            </div>
          {/if}

          {#if averagePosition !== undefined}
            <div class="bg-muted/50 p-4 rounded-lg border border-border">
              <div class="text-sm font-medium text-foreground">
                Avg. Position
              </div>
              <div class="text-2xl font-bold text-orange-600">
                #{Math.round(averagePosition)}
              </div>
            </div>
          {/if}

          {#if sentimentScore !== undefined}
            <div class="bg-muted/50 p-4 rounded-lg border border-border">
              <div class="text-sm font-medium text-foreground">
                Sentiment Score
              </div>
              <div class="text-2xl font-bold text-orange-600">
                {(sentimentScore * 100).toFixed(0)}%
              </div>
            </div>
          {/if}

          <div class="bg-muted/50 p-4 rounded-lg border border-border">
            <div class="text-sm font-medium text-foreground">
              Total Competitors
            </div>
            <div class="text-2xl font-bold text-orange-600">
              {currentRanking.competitors?.length || 0}
            </div>
          </div>
        </div>

        <!-- Rankings Table -->
        <div
          class="bg-background rounded-lg border border-border overflow-hidden"
        >
          <div class="px-6 py-4 border-b border-border">
            <h3 class="text-lg font-semibold text-foreground">
              {selectedProvider} Rankings
            </h3>
            <p class="text-sm text-muted-foreground">
              Brand visibility rankings for {selectedProvider}
            </p>
          </div>

          {#if currentRanking.competitors && currentRanking.competitors.length > 0}
            <div class="overflow-x-auto">
              <table class="w-full">
                <thead class="bg-muted/50">
                  <tr>
                    <th
                      class="px-6 py-3 text-left text-xs font-medium text-foreground uppercase tracking-wider"
                    >
                      Rank
                    </th>
                    <th
                      class="px-6 py-3 text-left text-xs font-medium text-foreground uppercase tracking-wider"
                    >
                      Company
                    </th>
                    <th
                      class="px-6 py-3 text-left text-xs font-medium text-foreground uppercase tracking-wider"
                    >
                      Visibility Score
                    </th>
                    <th
                      class="px-6 py-3 text-left text-xs font-medium text-foreground uppercase tracking-wider"
                    >
                      Mentions
                    </th>
                    <th
                      class="px-6 py-3 text-left text-xs font-medium text-foreground uppercase tracking-wider"
                    >
                      Avg. Position
                    </th>
                    <th
                      class="px-6 py-3 text-left text-xs font-medium text-foreground uppercase tracking-wider"
                    >
                      Sentiment
                    </th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-border">
                  {#each currentRanking.competitors
                    .sort((a, b) => (b.visibilityScore || 0) - (a.visibilityScore || 0))
                    .slice(0, 10) as competitor, index}
                    <tr class={competitor.isOwn ? "bg-orange-50" : ""}>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div
                          class="text-sm font-medium {competitor.isOwn
                            ? 'text-orange-600'
                            : 'text-foreground'}"
                        >
                          #{index + 1}
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center gap-3">
                          {#if competitor.isOwn}
                            <div
                              class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center"
                            >
                              <span class="text-orange-600 font-bold text-xs"
                                >YOU</span
                              >
                            </div>
                          {:else}
                            <div
                              class="w-8 h-8 bg-muted rounded-full flex items-center justify-center"
                            >
                              <span
                                class="text-muted-foreground font-medium text-xs"
                              >
                                {competitor.name.charAt(0).toUpperCase()}
                              </span>
                            </div>
                          {/if}
                          <div
                            class="text-sm font-medium {competitor.isOwn
                              ? 'text-orange-600'
                              : 'text-foreground'}"
                          >
                            {competitor.name}
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-foreground">
                          {competitor.visibilityScore || 0}%
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-foreground">
                          {competitor.mentions || 0}
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-foreground">
                          #{Math.round(competitor.averagePosition || 0)}
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        {#if competitor.sentiment}
                          {@const sentimentBadge = getSentimentBadge(
                            competitor.sentiment,
                          )}
                          <span
                            class="inline-flex px-2 py-1 text-xs font-medium rounded-full {sentimentBadge.class}"
                          >
                            {sentimentBadge.text}
                          </span>
                        {:else}
                          <span
                            class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800"
                          >
                            Neutral
                          </span>
                        {/if}
                      </td>
                    </tr>
                  {/each}
                </tbody>
              </table>
            </div>
          {:else}
            <div class="px-6 py-8 text-center">
              <p class="text-muted-foreground">
                No ranking data available for {selectedProvider}
              </p>
            </div>
          {/if}
        </div>
      </div>
    {/if}
  </div>
{/if}
