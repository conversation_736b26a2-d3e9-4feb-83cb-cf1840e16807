<script lang="ts">
  export let data: any[] = []
  export let brandName: string = ""
  export let competitors: any[] = []

  let sortColumn = "competitor"
  let sortDirection: "asc" | "desc" | null = "asc"

  // Get all providers from the data
  $: providers = data.length > 0 ? Object.keys(data[0].providers || {}) : []

  // Sort data based on current sort settings
  $: sortedData = [...data].sort((a, b) => {
    if (sortColumn === "competitor") {
      const aValue = a.competitor
      const bValue = b.competitor
      if (sortDirection === "asc") {
        return aValue.localeCompare(bValue)
      } else {
        return bValue.localeCompare(aValue)
      }
    } else {
      // Sorting by provider column
      const aValue = a.providers[sortColumn]?.visibilityScore || 0
      const bValue = b.providers[sortColumn]?.visibilityScore || 0
      if (sortDirection === "asc") {
        return aValue - bValue
      } else {
        return bValue - aValue
      }
    }
  })

  function handleSort(column: string) {
    if (sortColumn === column) {
      if (sortDirection === "asc") {
        sortDirection = "desc"
      } else if (sortDirection === "desc") {
        sortDirection = null
        sortColumn = "competitor"
      } else {
        sortDirection = "asc"
      }
    } else {
      sortColumn = column
      sortDirection = "asc"
    }
  }

  function getSortIcon(column: string) {
    if (sortColumn !== column) {
      return "↕️"
    }
    if (sortDirection === "asc") {
      return "↑"
    } else if (sortDirection === "desc") {
      return "↓"
    }
    return "↕️"
  }

  function getBackgroundStyle(score: number) {
    // Create a gradient from light orange to dark orange based on score
    const intensity = Math.min(score / 100, 1)
    const alpha = 0.1 + intensity * 0.3 // Range from 0.1 to 0.4
    return `background-color: rgba(234, 88, 12, ${alpha})`
  }

  function getCompetitorData(competitorName: string) {
    return competitors.find((c) => c.name === competitorName)
  }

  function getFallbackUrl(competitorName: string) {
    return `https://${competitorName.toLowerCase().replace(/\s+/g, "")}.com`
  }
</script>

{#if !data || data.length === 0}
  <div class="text-center py-12 bg-muted/50 rounded-lg">
    <p class="text-muted-foreground text-lg mb-2">No comparison data available</p>
    <p class="text-muted-foreground text-sm">
      The analysis may still be processing or no providers returned data.
    </p>
  </div>
{:else}
  <div class="overflow-x-auto rounded-lg border border-border">
    <table class="w-full border-collapse">
      <thead>
        <tr>
          <th class="bg-muted border-b border-r border-border w-[180px]">
            <button
              onclick={() => handleSort("competitor")}
              class="w-full p-3 font-medium text-foreground flex items-center justify-between hover:bg-muted/80 transition-colors text-left"
            >
              Competitors
              <span class="text-xs">{getSortIcon("competitor")}</span>
            </button>
          </th>
          {#each providers as provider, index}
            <th
              class="bg-muted border-b {index < providers.length - 1
                ? 'border-r'
                : ''} border-border"
            >
              <button
                onclick={() => handleSort(provider)}
                class="w-full p-3 font-medium text-foreground flex items-center justify-center hover:bg-muted/80 transition-colors"
              >
                <div class="flex items-center gap-2">
                  {#if provider === "OpenAI"}
                    <div class="w-5 h-5 bg-black rounded"></div>
                  {:else if provider === "Anthropic"}
                    <div class="w-5 h-5 bg-orange-500 rounded"></div>
                  {:else if provider === "Google"}
                    <div class="w-5 h-5 bg-blue-500 rounded"></div>
                  {:else if provider === "Perplexity"}
                    <div class="w-5 h-5 bg-purple-500 rounded"></div>
                  {:else}
                    <div class="w-5 h-5 bg-gray-400 rounded"></div>
                  {/if}
                  <span class="text-sm">{provider}</span>
                  <span class="text-xs">{getSortIcon(provider)}</span>
                </div>
              </button>
            </th>
          {/each}
        </tr>
      </thead>
      <tbody>
        {#each sortedData as competitor, rowIndex}
          <tr class={rowIndex > 0 ? "border-t border-border" : ""}>
            <td class="border-r border-border bg-background">
              <div class="p-3 flex items-center gap-3">
                {#if competitor.isOwn}
                  <div
                    class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center"
                  >
                    <span class="text-orange-600 font-bold text-xs">YOU</span>
                  </div>
                {:else}
                  <div class="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
                    <span class="text-muted-foreground font-medium text-xs">
                      {competitor.competitor.charAt(0).toUpperCase()}
                    </span>
                  </div>
                {/if}
                <div class="flex-1 min-w-0">
                  <div
                    class="font-medium text-sm {competitor.isOwn
                      ? 'text-orange-600'
                      : 'text-foreground'} truncate"
                  >
                    {competitor.competitor}
                  </div>
                  {#if !competitor.isOwn}
                    <div class="text-xs text-muted-foreground truncate">
                      {getCompetitorData(competitor.competitor)?.url ||
                        getFallbackUrl(competitor.competitor)}
                    </div>
                  {/if}
                </div>
              </div>
            </td>
            {#each providers as provider, index}
              {@const providerData = competitor.providers[provider]}
              {@const score = providerData?.visibilityScore || 0}
              <td
                class="text-center p-3 {index < providers.length - 1
                  ? 'border-r border-border'
                  : ''}"
                style={getBackgroundStyle(score)}
              >
                <span class="text-orange-900 font-medium text-xs">
                  {score}%
                </span>
                {#if providerData?.position}
                  <div class="text-xs text-muted-foreground mt-1">
                    #{providerData.position}
                  </div>
                {/if}
              </td>
            {/each}
          </tr>
        {/each}
      </tbody>
    </table>
  </div>

  <!-- Summary Stats -->
  {#if data.length > 0}
    <div class="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
      <div class="bg-muted/50 p-3 rounded-lg border border-border">
        <div class="text-sm font-medium text-foreground">Total Competitors</div>
        <div class="text-xl font-bold text-orange-600">{data.length}</div>
      </div>
      <div class="bg-muted/50 p-3 rounded-lg border border-border">
        <div class="text-sm font-medium text-foreground">AI Providers</div>
        <div class="text-xl font-bold text-orange-600">{providers.length}</div>
      </div>
      <div class="bg-muted/50 p-3 rounded-lg border border-border">
        <div class="text-sm font-medium text-foreground">Brand Rank</div>
        <div class="text-xl font-bold text-orange-600">
          #{data.findIndex((c) => c.isOwn) + 1}
        </div>
      </div>
      <div class="bg-muted/50 p-3 rounded-lg border border-border">
        <div class="text-sm font-medium text-foreground">Avg. Score</div>
        <div class="text-xl font-bold text-orange-600">
          {Math.round(
            data
              .find((c) => c.isOwn)
              ?.providers
              ? Object.values(data.find((c) => c.isOwn)?.providers || {})
                  .map((p: any) => p.visibilityScore || 0)
                  .reduce((a, b) => a + b, 0) /
                Object.keys(data.find((c) => c.isOwn)?.providers || {}).length
              : 0,
          )}%
        </div>
      </div>
    </div>
  {/if}
{/if}
