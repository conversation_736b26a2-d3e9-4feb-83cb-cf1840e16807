<script lang="ts">
  import { createEventDispatcher } from "svelte"
  import type { CompanyDiscoveryResponse } from "shared"

  export let discoveryData: CompanyDiscoveryResponse
  export let isLoading = false

  const dispatch = createEventDispatcher()

  // Editable state
  let editablePrompts = [...discoveryData.generatedPrompts]
  let editableCompetitors = [...discoveryData.suggestedCompetitors]
  let newPrompt = ""
  let newCompetitor = ""

  function addPrompt() {
    if (newPrompt.trim() && editablePrompts.length < 10) {
      editablePrompts = [...editablePrompts, newPrompt.trim()]
      newPrompt = ""
    }
  }

  function removePrompt(index: number) {
    editablePrompts = editablePrompts.filter((_, i) => i !== index)
  }

  function addCompetitor() {
    if (newCompetitor.trim() && editableCompetitors.length < 8) {
      editableCompetitors = [...editableCompetitors, newCompetitor.trim()]
      newCompetitor = ""
    }
  }

  function removeCompetitor(index: number) {
    editableCompetitors = editableCompetitors.filter((_, i) => i !== index)
  }

  function handleBack() {
    dispatch("back")
  }

  function handleProceedToAnalysis() {
    const finalData = {
      company: discoveryData.company,
      prompts: editablePrompts,
      competitors: editableCompetitors
    }
    dispatch("proceedToAnalysis", finalData)
  }

  function handleKeydown(event: KeyboardEvent, action: () => void) {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault()
      action()
    }
  }
</script>

<div class="card-brutal p-8">
  <div class="space-y-8">
    <!-- Header -->
    <div>
      <div class="flex items-center gap-3 mb-2">
        <button
          onclick={handleBack}
          class="p-2 hover:bg-muted rounded-lg transition-colors"
          title="Back to form"
        >
          <svg
            class="w-5 h-5 text-muted-foreground"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>
        <h2 class="text-2xl font-semibold text-foreground">
          Review Company Discovery
        </h2>
      </div>
      <p class="text-muted-foreground">
        Review and customize the extracted company information, generated prompts, and suggested competitors before starting the analysis.
      </p>
    </div>

    <!-- Company Information -->
    <div class="bg-muted/30 p-6 rounded-lg border border-border">
      <h3 class="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
        <svg class="w-5 h-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
        Company Information
      </h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="text-sm font-medium text-foreground">Company Name</label>
          <p class="text-foreground font-semibold">{discoveryData.company.name}</p>
        </div>
        <div>
          <label class="text-sm font-medium text-foreground">Website</label>
          <p class="text-muted-foreground break-all">{discoveryData.company.url}</p>
        </div>
        {#if discoveryData.company.industry}
          <div>
            <label class="text-sm font-medium text-foreground">Industry</label>
            <p class="text-muted-foreground">{discoveryData.company.industry}</p>
          </div>
        {/if}
        {#if discoveryData.company.description}
          <div class="md:col-span-2">
            <label class="text-sm font-medium text-foreground">Description</label>
            <p class="text-muted-foreground">{discoveryData.company.description}</p>
          </div>
        {/if}
      </div>
    </div>

    <!-- Generated Prompts -->
    <div class="bg-muted/30 p-6 rounded-lg border border-border">
      <h3 class="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
        <svg class="w-5 h-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        Analysis Prompts ({editablePrompts.length}/10)
      </h3>
      <p class="text-sm text-muted-foreground mb-4">
        These prompts will be used to test how AI models respond about your brand. You can edit, remove, or add new prompts.
      </p>
      
      <div class="space-y-3 mb-4">
        {#each editablePrompts as prompt, index}
          <div class="flex items-center gap-2 p-3 bg-background rounded-lg border border-border">
            <span class="flex-1 text-foreground">{prompt}</span>
            <button
              onclick={() => removePrompt(index)}
              class="p-1 text-destructive hover:bg-destructive/10 rounded transition-colors"
              title="Remove prompt"
            >
              <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        {/each}
      </div>

      {#if editablePrompts.length < 10}
        <div class="flex gap-2">
          <input
            bind:value={newPrompt}
            placeholder="Add a custom prompt..."
            class="flex-1 px-3 py-2 border border-border bg-background text-foreground placeholder-muted-foreground focus:border-primary focus:outline-none transition-colors rounded"
            onkeydown={(e) => handleKeydown(e, addPrompt)}
          />
          <button
            onclick={addPrompt}
            disabled={!newPrompt.trim()}
            class="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Add
          </button>
        </div>
      {/if}
    </div>

    <!-- Suggested Competitors -->
    <div class="bg-muted/30 p-6 rounded-lg border border-border">
      <h3 class="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
        <svg class="w-5 h-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
        Competitors ({editableCompetitors.length}/8)
      </h3>
      <p class="text-sm text-muted-foreground mb-4">
        These competitors will be included in the analysis for comparison. You can edit, remove, or add new competitors.
      </p>
      
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 mb-4">
        {#each editableCompetitors as competitor, index}
          <div class="flex items-center gap-2 p-2 bg-background rounded border border-border">
            <span class="flex-1 text-foreground text-sm">{competitor}</span>
            <button
              onclick={() => removeCompetitor(index)}
              class="p-1 text-destructive hover:bg-destructive/10 rounded transition-colors"
              title="Remove competitor"
            >
              <svg class="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        {/each}
      </div>

      {#if editableCompetitors.length < 8}
        <div class="flex gap-2">
          <input
            bind:value={newCompetitor}
            placeholder="Add a competitor..."
            class="flex-1 px-3 py-2 border border-border bg-background text-foreground placeholder-muted-foreground focus:border-primary focus:outline-none transition-colors rounded"
            onkeydown={(e) => handleKeydown(e, addCompetitor)}
          />
          <button
            onclick={addCompetitor}
            disabled={!newCompetitor.trim()}
            class="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Add
          </button>
        </div>
      {/if}
    </div>

    <!-- Action Buttons -->
    <div class="flex items-center justify-between pt-4 border-t border-border">
      <div class="text-sm text-muted-foreground">
        Ready to proceed with {editablePrompts.length} prompts and {editableCompetitors.length} competitors
      </div>
      
      <div class="flex gap-3">
        <button
          onclick={handleBack}
          class="btn-secondary px-6 py-3"
        >
          Back to Edit
        </button>
        <button
          onclick={handleProceedToAnalysis}
          disabled={isLoading || editablePrompts.length === 0}
          class="btn-primary px-8 py-3 font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {#if isLoading}
            <div class="flex items-center gap-2">
              <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              Starting Analysis...
            </div>
          {:else}
            Start Brand Analysis
          {/if}
        </button>
      </div>
    </div>
  </div>
</div>

<style>
  .btn-primary {
    @apply bg-primary text-primary-foreground border-2 border-border shadow-brutal-sm hover:shadow-brutal-md hover:-translate-x-0.5 hover:-translate-y-0.5 transition-all duration-200;
  }
  
  .btn-secondary {
    @apply bg-secondary text-secondary-foreground border-2 border-border shadow-brutal-sm hover:shadow-brutal-md hover:-translate-x-0.5 hover:-translate-y-0.5 transition-all duration-200;
  }
  
  .card-brutal {
    @apply bg-card border-2 border-border shadow-brutal-sm;
  }
</style>
