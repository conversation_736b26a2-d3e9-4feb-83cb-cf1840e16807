<script lang="ts">
  export let prompts: any[] = []
  export let responses: any[] = []
  export let brandName: string = ""

  let selectedPrompt: any = null
  let expandedResponses: Set<string> = new Set()

  // Group responses by prompt
  $: responsesByPrompt = responses.reduce((acc, response) => {
    const promptKey = response.prompt || "Unknown Prompt"
    if (!acc[promptKey]) {
      acc[promptKey] = []
    }
    acc[promptKey].push(response)
    return acc
  }, {})

  // Get unique prompts from responses if prompts array is empty
  $: availablePrompts = prompts.length > 0 
    ? prompts 
    : Object.keys(responsesByPrompt).map(prompt => ({ prompt, category: 'general' }))

  function selectPrompt(prompt: any) {
    selectedPrompt = prompt
  }

  function toggleResponse(responseId: string) {
    if (expandedResponses.has(responseId)) {
      expandedResponses.delete(responseId)
    } else {
      expandedResponses.add(responseId)
    }
    expandedResponses = expandedResponses // Trigger reactivity
  }

  function getProviderIcon(provider: string) {
    switch (provider) {
      case "OpenAI":
        return "🤖"
      case "Anthropic":
        return "🧠"
      case "Google":
        return "🔍"
      case "Perplexity":
        return "🔮"
      default:
        return "🤖"
    }
  }

  function getSentimentColor(sentiment: string) {
    switch (sentiment) {
      case "positive":
        return "text-green-600 bg-green-100"
      case "negative":
        return "text-red-600 bg-red-100"
      default:
        return "text-gray-600 bg-gray-100"
    }
  }

  function formatResponse(response: string) {
    // Truncate long responses for preview
    if (response.length > 300) {
      return response.substring(0, 300) + "..."
    }
    return response
  }

  function getCategoryColor(category: string) {
    switch (category) {
      case "ranking":
        return "bg-blue-100 text-blue-800"
      case "comparison":
        return "bg-purple-100 text-purple-800"
      case "alternatives":
        return "bg-green-100 text-green-800"
      case "recommendations":
        return "bg-orange-100 text-orange-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }
</script>

<div class="space-y-6">
  {#if availablePrompts.length === 0}
    <div class="text-center py-12 bg-muted/50 rounded-lg">
      <p class="text-muted-foreground text-lg mb-2">No prompts or responses available</p>
      <p class="text-muted-foreground text-sm">
        The analysis may still be processing or no prompt data was returned.
      </p>
    </div>
  {:else}
    <!-- Prompt Selection -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold text-foreground">Select a Prompt</h3>
      <div class="grid gap-3">
        {#each availablePrompts as prompt}
          <button
            onclick={() => selectPrompt(prompt)}
            class="text-left p-4 rounded-lg border border-border hover:bg-muted/50 transition-colors {selectedPrompt?.prompt === prompt.prompt ? 'bg-orange-50 border-orange-200' : 'bg-background'}"
          >
            <div class="flex items-start justify-between gap-4">
              <div class="flex-1">
                <div class="font-medium text-foreground mb-1">
                  {prompt.prompt}
                </div>
                {#if prompt.category}
                  <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full {getCategoryColor(prompt.category)}">
                    {prompt.category}
                  </span>
                {/if}
              </div>
              <div class="text-sm text-muted-foreground">
                {responsesByPrompt[prompt.prompt]?.length || 0} responses
              </div>
            </div>
          </button>
        {/each}
      </div>
    </div>

    <!-- Selected Prompt Responses -->
    {#if selectedPrompt}
      {@const promptResponses = responsesByPrompt[selectedPrompt.prompt] || []}
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-foreground">
            AI Provider Responses
          </h3>
          <div class="text-sm text-muted-foreground">
            {promptResponses.length} responses
          </div>
        </div>

        {#if promptResponses.length === 0}
          <div class="text-center py-8 bg-muted/50 rounded-lg">
            <p class="text-muted-foreground">No responses available for this prompt</p>
          </div>
        {:else}
          <div class="space-y-4">
            {#each promptResponses as response, index}
              {@const responseId = `${response.provider}-${index}`}
              {@const isExpanded = expandedResponses.has(responseId)}
              <div class="bg-background rounded-lg border border-border overflow-hidden">
                <!-- Response Header -->
                <div class="p-4 border-b border-border">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <span class="text-lg">{getProviderIcon(response.provider)}</span>
                      <div>
                        <div class="font-medium text-foreground">{response.provider}</div>
                        <div class="text-sm text-muted-foreground">
                          {new Date(response.timestamp).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                    <div class="flex items-center gap-2">
                      {#if response.brandMentioned}
                        <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                          Brand Mentioned
                        </span>
                        {#if response.brandPosition}
                          <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                            Position #{response.brandPosition}
                          </span>
                        {/if}
                      {:else}
                        <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">
                          Not Mentioned
                        </span>
                      {/if}
                      <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full {getSentimentColor(response.sentiment)}">
                        {response.sentiment}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- Response Content -->
                <div class="p-4">
                  <div class="text-sm text-foreground leading-relaxed">
                    {isExpanded ? response.response : formatResponse(response.response)}
                  </div>
                  
                  {#if response.response.length > 300}
                    <button
                      onclick={() => toggleResponse(responseId)}
                      class="mt-2 text-sm text-orange-600 hover:text-orange-700 font-medium"
                    >
                      {isExpanded ? "Show Less" : "Show More"}
                    </button>
                  {/if}

                  <!-- Additional Details -->
                  {#if response.competitors && response.competitors.length > 0}
                    <div class="mt-4 pt-4 border-t border-border">
                      <div class="text-sm font-medium text-foreground mb-2">
                        Competitors Mentioned:
                      </div>
                      <div class="flex flex-wrap gap-2">
                        {#each response.competitors as competitor}
                          <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-muted text-muted-foreground">
                            {competitor}
                          </span>
                        {/each}
                      </div>
                    </div>
                  {/if}

                  {#if response.confidence !== undefined}
                    <div class="mt-4 pt-4 border-t border-border">
                      <div class="flex items-center justify-between text-sm">
                        <span class="text-muted-foreground">Confidence Score:</span>
                        <span class="font-medium text-foreground">
                          {(response.confidence * 100).toFixed(0)}%
                        </span>
                      </div>
                    </div>
                  {/if}
                </div>
              </div>
            {/each}
          </div>
        {/if}
      </div>
    {:else}
      <div class="text-center py-8 bg-muted/50 rounded-lg">
        <p class="text-muted-foreground">Select a prompt above to view AI responses</p>
      </div>
    {/if}
  {/if}
</div>
