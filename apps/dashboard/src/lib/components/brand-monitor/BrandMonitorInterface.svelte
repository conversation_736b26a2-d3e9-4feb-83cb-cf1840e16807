<script lang="ts">
  import { createEventDispatcher } from "svelte"
  import UrlInputSection from "./UrlInputSection.svelte"
  import AnalysisResults from "./AnalysisResults.svelte"
  import AnalysisList from "./AnalysisList.svelte"
  import EmptyState from "./EmptyState.svelte"
  import ErrorDisplay from "./ErrorDisplay.svelte"
  import RealTimeAnalysis from "./RealTimeAnalysis.svelte"
  import type { BrandAnalysis, BrandAnalysisRequest } from "shared"

  export let analyses: BrandAnalysis[] = []
  export let session: any
  export let error: string | null = null

  const dispatch = createEventDispatcher()

  let selectedAnalysis: BrandAnalysis | null = null
  let isAnalyzing = false
  let currentAnalysisData: BrandAnalysisRequest | null = null
  let showInputForm = false

  function handleStartAnalysis(event: CustomEvent) {
    currentAnalysisData = event.detail
    isAnalyzing = true
  }

  function handleAnalysisComplete(result: any) {
    isAnalyzing = false
    currentAnalysisData = null
    // Add the completed analysis to the list
    if (result) {
      analyses = [result, ...analyses]
      dispatch("newAnalysis", result)
    }
  }

  function handleAnalysisCancel() {
    isAnalyzing = false
    currentAnalysisData = null
  }

  function selectAnalysis(analysis: BrandAnalysis) {
    selectedAnalysis = analysis
  }

  function clearSelection() {
    selectedAnalysis = null
    // If no analyses exist, go back to empty state
    if (analyses.length === 0) {
      showInputForm = false
    }
  }

  function handleErrorRetry() {
    dispatch("retry")
  }

  function handleErrorDismiss() {
    dispatch("dismissError")
  }

  function handleNewAnalysisFromEmpty() {
    clearSelection()
    showInputForm = true
  }
</script>

<div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
  <!-- Sidebar with analysis list -->
  <div class="lg:col-span-1">
    <AnalysisList
      {analyses}
      {selectedAnalysis}
      on:select={(e) => selectAnalysis(e.detail)}
      on:clearSelection={clearSelection}
    />
  </div>

  <!-- Main content area -->
  <div class="lg:col-span-3 space-y-6">
    <!-- Error Display -->
    {#if error}
      <ErrorDisplay
        {error}
        title="Connection Error"
        on:retry={handleErrorRetry}
        on:dismiss={handleErrorDismiss}
      />
    {/if}

    <!-- Main Content -->
    {#if selectedAnalysis}
      <AnalysisResults analysis={selectedAnalysis} on:back={clearSelection} />
    {:else if isAnalyzing && currentAnalysisData && session}
      <RealTimeAnalysis
        analysisData={currentAnalysisData}
        supabaseToken={session.access_token}
        onComplete={handleAnalysisComplete}
        onCancel={handleAnalysisCancel}
      />
    {:else if showInputForm || analyses.length > 0}
      <UrlInputSection
        {isAnalyzing}
        on:startAnalysis={handleStartAnalysis}
        on:complete={handleAnalysisComplete}
        on:back={clearSelection}
      />
    {:else}
      <EmptyState
        title="Start Your First Brand Analysis"
        description="Enter your company's website URL to analyze how AI models like ChatGPT, Claude, and Perplexity represent your brand in their responses."
        actionText="Start Analysis"
        on:action={handleNewAnalysisFromEmpty}
      />
    {/if}
  </div>
</div>
