<script lang="ts">
  import { createEventDispatcher } from "svelte"
  import type { BrandAnalysis } from "shared"
  import VisibilityScore from "./VisibilityScore.svelte"
  import ProviderComparisonMatrix from "./ProviderComparisonMatrix.svelte"
  import ProviderRankings from "./ProviderRankings.svelte"
  import PromptsResponses from "./PromptsResponses.svelte"

  export let analysis: BrandAnalysis

  const dispatch = createEventDispatcher()

  let activeTab = "visibility"

  function goBack() {
    dispatch("back")
  }

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  function getAnalysisStatus() {
    if (analysis.analysisData) {
      return {
        status: "completed",
        color: "text-green-600",
        bgColor: "bg-green-100",
      }
    }
    return {
      status: "in_progress",
      color: "text-yellow-600",
      bgColor: "bg-yellow-100",
    }
  }

  // Extract data from analysis for components
  $: analysisData = analysis.analysisData || {}
  $: competitors = analysisData.competitors || []
  $: brandData = competitors.find((c: any) => c.isOwn) || {
    visibilityScore: analysisData.scores?.visibilityScore || 0,
    shareOfVoice: analysisData.scores?.shareOfVoice || 0,
    averagePosition: analysisData.scores?.averagePosition || 0,
    sentimentScore: analysisData.scores?.sentimentScore || 0,
  }
  $: providerComparison = analysisData.providerComparison || []
  $: providerRankings = analysisData.providerRankings || []
  $: prompts = analysisData.prompts || []
  $: responses = analysisData.responses || []
  $: statusInfo = getAnalysisStatus()

  // Debug logging
  $: if (analysis.analysisData) {
    console.log("[AnalysisResults] Analysis data:", analysisData)
    console.log("[AnalysisResults] Competitors:", competitors)
    console.log("[AnalysisResults] Brand data:", brandData)
    console.log("[AnalysisResults] Provider comparison:", providerComparison)
    console.log("[AnalysisResults] Provider rankings:", providerRankings)
    console.log("[AnalysisResults] Prompts:", prompts)
    console.log("[AnalysisResults] Responses:", responses)
  }

  function setActiveTab(tab: string) {
    activeTab = tab
  }
</script>

<div class="space-y-6">
  <!-- Header -->
  <div class="card-brutal p-6">
    <div class="flex items-start justify-between mb-4">
      <button
        onclick={goBack}
        class="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
      >
        <svg
          class="w-4 h-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 19l-7-7 7-7"
          />
        </svg>
        Back to analyses
      </button>

      <div class="flex items-center gap-2">
        <div
          class="px-3 py-1 rounded-full text-xs font-medium {statusInfo.bgColor} {statusInfo.color}"
        >
          {statusInfo.status === "completed" ? "Completed" : "In Progress"}
        </div>
      </div>
    </div>

    <div class="space-y-3">
      <h1 class="text-2xl font-semibold text-foreground">
        {analysis.companyName || "Brand Analysis"}
      </h1>

      <div class="flex items-center gap-4 text-sm text-muted-foreground">
        <div class="flex items-center gap-1">
          <svg
            class="w-4 h-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
            />
          </svg>
          <a
            href={analysis.url}
            target="_blank"
            rel="noopener noreferrer"
            class="hover:text-foreground transition-colors"
          >
            {analysis.url}
          </a>
        </div>

        <div class="flex items-center gap-1">
          <svg
            class="w-4 h-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          {formatDate(analysis.createdAt)}
        </div>
      </div>

      {#if analysis.competitors && Array.isArray(analysis.competitors) && analysis.competitors.length > 0}
        <div>
          <h3 class="text-sm font-medium text-foreground mb-2">
            Competitors Analyzed
          </h3>
          <div class="flex flex-wrap gap-2">
            {#each analysis.competitors as competitor}
              <span
                class="px-2 py-1 bg-muted text-muted-foreground text-xs rounded border"
              >
                {typeof competitor === "string"
                  ? competitor
                  : competitor.name || "Unknown"}
              </span>
            {/each}
          </div>
        </div>
      {/if}
    </div>
  </div>

  <!-- Analysis Content -->
  {#if statusInfo.status === "completed" && analysis.analysisData}
    <!-- Results Section with Tabs -->
    <div class="card-brutal p-6">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold text-foreground">Analysis Results</h2>
        <div class="text-right">
          <p class="text-2xl font-bold text-orange-600">
            {brandData.visibilityScore}%
          </p>
          <p class="text-xs text-muted-foreground mt-1">Average Score</p>
        </div>
      </div>

      <!-- Tab Navigation -->
      <div class="border-b border-border mb-6">
        <nav class="flex space-x-8">
          <button
            onclick={() => setActiveTab("visibility")}
            class="py-2 px-1 border-b-2 font-medium text-sm {activeTab ===
            'visibility'
              ? 'border-orange-500 text-orange-600'
              : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'}"
          >
            Visibility Score
          </button>
          <button
            onclick={() => setActiveTab("matrix")}
            class="py-2 px-1 border-b-2 font-medium text-sm {activeTab ===
            'matrix'
              ? 'border-orange-500 text-orange-600'
              : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'}"
          >
            Comparison Matrix
          </button>
          <button
            onclick={() => setActiveTab("rankings")}
            class="py-2 px-1 border-b-2 font-medium text-sm {activeTab ===
            'rankings'
              ? 'border-orange-500 text-orange-600'
              : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'}"
          >
            Provider Rankings
          </button>
          <button
            onclick={() => setActiveTab("prompts")}
            class="py-2 px-1 border-b-2 font-medium text-sm {activeTab ===
            'prompts'
              ? 'border-orange-500 text-orange-600'
              : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'}"
          >
            Prompts & Responses
          </button>
        </nav>
      </div>

      <!-- Tab Content -->
      <div class="min-h-[400px]">
        {#if activeTab === "visibility"}
          <VisibilityScore
            {brandData}
            {competitors}
            brandName={analysis.companyName || ""}
          />
        {:else if activeTab === "matrix"}
          <ProviderComparisonMatrix
            data={providerComparison}
            brandName={analysis.companyName || ""}
            {competitors}
          />
        {:else if activeTab === "rankings"}
          <ProviderRankings
            {providerRankings}
            brandName={analysis.companyName || ""}
            shareOfVoice={brandData.shareOfVoice}
            averagePosition={brandData.averagePosition}
            sentimentScore={brandData.sentimentScore}
            weeklyChange={brandData.weeklyChange}
          />
        {:else if activeTab === "prompts"}
          <PromptsResponses
            {prompts}
            {responses}
            brandName={analysis.companyName || ""}
          />
        {/if}
      </div>
    </div>
  {:else}
    <!-- In Progress Section -->
    <div class="card-brutal p-6">
      <div class="text-center space-y-4">
        <div
          class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto"
        >
          <div
            class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"
          ></div>
        </div>

        <div>
          <h2 class="text-xl font-semibold text-foreground mb-2">
            Analysis in Progress
          </h2>
          <p class="text-muted-foreground">
            We're analyzing your brand representation across multiple AI models.
            This typically takes 2-5 minutes.
          </p>
        </div>

        <div class="max-w-md mx-auto">
          <div class="bg-muted rounded-full h-2">
            <div
              class="bg-blue-600 h-2 rounded-full animate-pulse"
              style="width: 60%"
            ></div>
          </div>
          <p class="text-xs text-muted-foreground mt-2">
            Analyzing AI model responses...
          </p>
        </div>
      </div>
    </div>
  {/if}

  <!-- Actions -->
  <div class="card-brutal p-4">
    <div class="flex items-center justify-between">
      <div class="text-sm text-muted-foreground">
        Analysis ID: {analysis.id}
      </div>

      <div class="flex gap-2">
        {#if statusInfo.status === "completed"}
          <button class="btn-secondary px-4 py-2 text-sm">
            Export Results
          </button>
          <button class="btn-secondary px-4 py-2 text-sm">
            Share Analysis
          </button>
        {:else}
          <button class="btn-secondary px-4 py-2 text-sm">
            Refresh Status
          </button>
        {/if}
      </div>
    </div>
  </div>
</div>

<style>
  .btn-secondary {
    background-color: #6b7280;
    color: white;
    border: 2px solid #1f2937;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s;
  }

  .btn-secondary:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    transform: translate(-2px, -2px);
  }

  .card-brutal {
    background-color: white;
    border: 2px solid #1f2937;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  }
</style>
