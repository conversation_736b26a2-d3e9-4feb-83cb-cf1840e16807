<script lang="ts">
  import { onMount } from "svelte"
  import { Chart, registerables } from "chart.js"

  export let brandData: any
  export let competitors: any[] = []
  export let brandName: string = ""

  let chartCanvas: HTMLCanvasElement
  let chart: Chart | null = null

  // Register Chart.js components
  Chart.register(...registerables)

  onMount(() => {
    if (chartCanvas && competitors.length > 0) {
      createPieChart()
    }

    return () => {
      if (chart) {
        chart.destroy()
      }
    }
  })

  function createPieChart() {
    if (chart) {
      chart.destroy()
    }

    const chartData = competitors.slice(0, 8).map((competitor) => ({
      name: competitor.name,
      value: competitor.visibilityScore || 0,
      isOwn: competitor.isOwn || false,
    }))

    const colors = [
      "#3b82f6",
      "#8b5cf6",
      "#ec4899",
      "#10b981",
      "#f59e0b",
      "#6366f1",
      "#14b8a6",
      "#f43f5e",
    ]

    chart = new Chart(chartCanvas, {
      type: "doughnut",
      data: {
        labels: chartData.map((d) => d.name),
        datasets: [
          {
            data: chartData.map((d) => d.value),
            backgroundColor: chartData.map((d, idx) =>
              d.isOwn ? "#ea580c" : colors[idx % colors.length],
            ),
            borderColor: chartData.map((d) => (d.isOwn ? "#ea580c" : "#ffffff")),
            borderWidth: chartData.map((d) => (d.isOwn ? 3 : 1)),
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false,
          },
          tooltip: {
            callbacks: {
              label: function (context) {
                return `${context.label}: ${context.parsed}%`
              },
            },
          },
        },
      },
    })
  }

  $: if (chartCanvas && competitors.length > 0) {
    createPieChart()
  }

  function getBrandRank() {
    return competitors.findIndex((c) => c.isOwn) + 1
  }

  function getTopCompetitor() {
    return competitors.filter((c) => !c.isOwn)[0]
  }

  function getDifference() {
    const topCompetitor = getTopCompetitor()
    return topCompetitor
      ? (brandData?.visibilityScore || 0) - topCompetitor.visibilityScore
      : 0
  }
</script>

<div class="flex flex-col h-full">
  <!-- Main Content Card -->
  <div class="card-brutal p-6 h-full flex flex-col">
    <div class="border-b border-border pb-4 mb-6">
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-xl font-semibold text-foreground">Visibility Score</h2>
          <p class="text-sm text-muted-foreground mt-1">
            Your brand visibility across AI providers
          </p>
        </div>
        <!-- Visibility Score in top right -->
        <div class="text-right">
          <p class="text-3xl font-bold text-orange-600">
            {brandData?.visibilityScore || 0}%
          </p>
          <p class="text-xs text-muted-foreground mt-1">Overall Score</p>
        </div>
      </div>
    </div>

    <div class="flex-1 flex gap-6">
      <!-- Chart Section -->
      <div class="flex-1">
        <div class="relative h-64 mb-4">
          {#if competitors.length > 0}
            <canvas bind:this={chartCanvas} class="w-full h-full"></canvas>
          {:else}
            <div
              class="w-full h-full flex items-center justify-center bg-muted/50 rounded-lg"
            >
              <p class="text-muted-foreground">No competitor data available</p>
            </div>
          {/if}
        </div>

        <!-- Chart Legend -->
        {#if competitors.length > 0}
          <div class="grid grid-cols-2 gap-2 text-sm">
            {#each competitors.slice(0, 8) as competitor, idx}
              <div class="flex items-center gap-2">
                <div
                  class="w-3 h-3 rounded-full"
                  style="background-color: {competitor.isOwn
                    ? '#ea580c'
                    : [
                        '#3b82f6',
                        '#8b5cf6',
                        '#ec4899',
                        '#10b981',
                        '#f59e0b',
                        '#6366f1',
                        '#14b8a6',
                        '#f43f5e',
                      ][idx % 8]}"
                ></div>
                <span
                  class="text-xs {competitor.isOwn
                    ? 'font-semibold text-orange-600'
                    : 'text-muted-foreground'}"
                >
                  {competitor.name}
                </span>
              </div>
            {/each}
          </div>
        {/if}
      </div>

      <!-- Stats Section -->
      <div class="w-80 space-y-4">
        <!-- Brand Position -->
        <div class="bg-muted/50 p-4 rounded-lg border border-border">
          <div class="flex justify-between items-center mb-2">
            <span class="text-sm font-medium text-foreground">Brand Rank</span>
            <span class="text-2xl font-bold text-orange-600">
              #{getBrandRank()}
            </span>
          </div>
          <p class="text-xs text-muted-foreground">
            Out of {competitors.length} competitors
          </p>
        </div>

        <!-- Performance vs Top Competitor -->
        {#if getTopCompetitor()}
          <div class="bg-muted/50 p-4 rounded-lg border border-border">
            <div class="flex justify-between items-center mb-2">
              <span class="text-sm font-medium text-foreground">
                vs. Top Competitor
              </span>
              <span
                class="text-lg font-bold {getDifference() >= 0
                  ? 'text-green-600'
                  : 'text-red-600'}"
              >
                {getDifference() >= 0 ? '+' : ''}{getDifference().toFixed(1)}%
              </span>
            </div>
            <p class="text-xs text-muted-foreground">
              Compared to {getTopCompetitor().name}
            </p>
          </div>
        {/if}

        <!-- Additional Metrics -->
        {#if brandData}
          <div class="space-y-3">
            {#if brandData.shareOfVoice !== undefined}
              <div class="flex justify-between items-center">
                <span class="text-sm text-muted-foreground">Share of Voice</span>
                <span class="text-sm font-medium text-foreground">
                  {brandData.shareOfVoice.toFixed(1)}%
                </span>
              </div>
            {/if}

            {#if brandData.averagePosition !== undefined}
              <div class="flex justify-between items-center">
                <span class="text-sm text-muted-foreground">Avg. Position</span>
                <span class="text-sm font-medium text-foreground">
                  #{Math.round(brandData.averagePosition)}
                </span>
              </div>
            {/if}

            {#if brandData.sentimentScore !== undefined}
              <div class="flex justify-between items-center">
                <span class="text-sm text-muted-foreground">Sentiment</span>
                <span
                  class="text-sm font-medium {brandData.sentimentScore > 0.6
                    ? 'text-green-600'
                    : brandData.sentimentScore > 0.4
                      ? 'text-yellow-600'
                      : 'text-red-600'}"
                >
                  {(brandData.sentimentScore * 100).toFixed(0)}%
                </span>
              </div>
            {/if}
          </div>
        {/if}
      </div>
    </div>
  </div>
</div>

<style>
  .card-brutal {
    background-color: white;
    border: 2px solid #1f2937;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  }
</style>
