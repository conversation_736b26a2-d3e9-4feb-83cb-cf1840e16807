import { writable, derived, get } from "svelte/store"
import { fireGeoClient, startBrandAnalysis } from "$lib/firegeo-client"
import type { BrandAnalysisResponse, AnalysisProgress } from "shared"
import {
  getAnalysisSession,
  type AnalysisSession,
} from "$lib/supabase-analysis"

export interface AnalysisState {
  id: string | null
  status:
    | "idle"
    | "starting"
    | "in_progress"
    | "completed"
    | "failed"
    | "retrying"
  progress: AnalysisProgress | null
  result: BrandAnalysisResponse | null
  error: string | null
  retryCount: number
  lastError: string | null
  connectionLost: boolean
}

export function useRealTimeAnalysis() {
  // Core state
  const analysisState = writable<AnalysisState>({
    id: null,
    status: "idle",
    progress: null,
    result: null,
    error: null,
    retryCount: 0,
    lastError: null,
    connectionLost: false,
  })

  // Polling interval for progress updates (45 seconds)
  let pollingInterval: NodeJS.Timeout | null = null
  const POLLING_INTERVAL_MS = 45000 // 45 seconds

  // Derived stores for convenience
  const isAnalyzing = derived(
    analysisState,
    ($state) =>
      $state.status === "starting" ||
      $state.status === "in_progress" ||
      $state.status === "retrying",
  )

  const progressPercentage = derived(
    analysisState,
    ($state) => $state.progress?.progress || 0,
  )

  const currentStep = derived(
    analysisState,
    ($state) => $state.progress?.step || "",
  )

  // Start a new analysis with real-time updates
  async function startAnalysis(
    analysisData: any,
    supabaseToken: string,
  ): Promise<void> {
    try {
      // Reset state
      analysisState.update((state) => ({
        ...state,
        status: "starting",
        progress: null,
        result: null,
        error: null,
      }))

      // Start the analysis using the dashboard wrapper function
      console.log(
        "[useRealTimeAnalysis] Starting analysis with data:",
        analysisData,
      )
      const response = await startBrandAnalysis(
        analysisData.url,
        supabaseToken,
        {
          companyName: analysisData.companyName,
          competitors: analysisData.competitors,
          prompts: analysisData.prompts,
        },
      )

      analysisState.update((state) => ({
        ...state,
        id: response.id,
        status: "in_progress",
      }))

      // Use SSE for real-time updates instead of polling
      setupSSEConnection(response.analysisRequest)
    } catch (error) {
      console.error("Failed to start analysis:", error)
      analysisState.update((state) => ({
        ...state,
        status: "failed",
        error:
          error instanceof Error ? error.message : "Failed to start analysis",
      }))
    }
  }

  // Set up SSE connection for real-time updates
  function setupSSEConnection(analysisRequest: any) {
    console.log("[useRealTimeAnalysis] Setting up SSE connection")

    // Clear existing polling if any
    if (pollingInterval) {
      clearInterval(pollingInterval)
    }

    // Start SSE connection to the /analyze endpoint
    startSSEAnalysis(analysisRequest)
  }

  // Simple SSE Parser class for dashboard
  class SSEParser {
    private buffer = ""
    private currentEvent: { event?: string; data?: string } = {}

    parse(chunk: string): Array<{ event?: string; data?: string }> {
      this.buffer += chunk
      const lines = this.buffer.split("\n")
      const events: Array<{ event?: string; data?: string }> = []

      // Keep the last line if it's incomplete
      this.buffer = lines[lines.length - 1]

      // Process all complete lines
      for (let i = 0; i < lines.length - 1; i++) {
        const line = lines[i].trim()

        if (line === "") {
          // Empty line signals end of event
          if (this.currentEvent.data) {
            events.push({ ...this.currentEvent })
            this.currentEvent = {}
          }
          continue
        }

        if (line.startsWith("event:")) {
          this.currentEvent.event = line.slice(6).trim()
        } else if (line.startsWith("data:")) {
          this.currentEvent.data = line.slice(5).trim()
        }
      }

      return events
    }

    reset() {
      this.buffer = ""
    }
  }

  // Start analysis with SSE
  async function startSSEAnalysis(analysisRequest: any) {
    try {
      const apiUrl = "http://localhost:3001" // Use the FireGeo API URL
      const response = await fetch(`${apiUrl}/api/brand-monitor/analyze`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-API-Secret": "RobynnDevSecretKey",
        },
        body: JSON.stringify(analysisRequest),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error("No response body")
      }

      const decoder = new TextDecoder()
      const parser = new SSEParser()

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const events = parser.parse(chunk)

        for (const event of events) {
          if (event.data) {
            try {
              const eventData = JSON.parse(event.data)
              console.log("[useRealTimeAnalysis] Parsed SSE event:", eventData)
              handleSSEEvent(eventData)
            } catch (e) {
              console.error(
                "Failed to parse SSE event data:",
                e,
                "Raw data:",
                event.data,
              )
            }
          }
        }
      }
    } catch (error) {
      console.error("SSE connection failed:", error)
      analysisState.update((state) => ({
        ...state,
        status: "failed",
        error: error instanceof Error ? error.message : "Connection failed",
      }))
    }
  }

  // Handle SSE events
  function handleSSEEvent(eventData: any) {
    console.log("[useRealTimeAnalysis] SSE event received:", eventData)

    switch (eventData.type) {
      case "start":
        analysisState.update((state) => ({
          ...state,
          status: "in_progress",
          progress: {
            progress: 0,
            step: eventData.data?.message || "Starting analysis...",
            completed: false,
          },
        }))
        break

      case "stage":
        const stageProgress = eventData.data?.progress || 0
        analysisState.update((state) => ({
          ...state,
          progress: {
            progress: stageProgress,
            step: eventData.data?.message || "Processing...",
            completed: false,
          },
        }))

        // Auto-complete if we reach 100% but don't get a complete event
        if (stageProgress >= 100) {
          setTimeout(() => {
            const currentState = get(analysisState)
            if (
              currentState.status === "in_progress" &&
              currentState.progress?.progress >= 100
            ) {
              console.log(
                "[useRealTimeAnalysis] Auto-completing analysis after reaching 100%",
              )
              handleSSEEvent({ type: "complete", data: {} })
            }
          }, 2000) // Wait 2 seconds for a proper complete event
        }
        break

      case "progress":
        const progressValue = eventData.data?.progress || 0
        analysisState.update((state) => ({
          ...state,
          progress: {
            progress: progressValue,
            step: eventData.data?.message || "Processing...",
            completed: false,
          },
        }))

        // Auto-complete if we reach 100% but don't get a complete event
        if (progressValue >= 100) {
          setTimeout(() => {
            const currentState = get(analysisState)
            if (
              currentState.status === "in_progress" &&
              currentState.progress?.progress >= 100
            ) {
              console.log(
                "[useRealTimeAnalysis] Auto-completing analysis after reaching 100%",
              )
              handleSSEEvent({ type: "complete", data: {} })
            }
          }, 2000) // Wait 2 seconds for a proper complete event
        }
        break

      case "complete":
        // Extract analysis data from the event
        const analysisData = eventData.data?.analysis
        console.log(
          "[useRealTimeAnalysis] Complete event analysis data:",
          analysisData,
        )

        // Create a proper result structure with safe defaults
        const result = {
          id: analysisData?.id || `analysis-${Date.now()}`,
          companyName: analysisData?.company?.name || "Unknown Company",
          url: analysisData?.company?.url || "",
          industry: analysisData?.company?.industry || "",
          overallScore: analysisData?.overallScore || 0,
          rankings: analysisData?.rankings || [],
          insights: analysisData?.insights || [],
          competitors: analysisData?.knownCompetitors || [],
          prompts: analysisData?.prompts || [],
          analysisData: analysisData, // Include the full analysis data
          createdAt: new Date().toISOString(),
        }

        console.log("[useRealTimeAnalysis] Setting completed result:", result)

        analysisState.update((state) => ({
          ...state,
          status: "completed",
          progress: {
            progress: 100,
            step: "Analysis complete!",
            completed: true,
          },
          result,
        }))
        break

      case "error":
        analysisState.update((state) => ({
          ...state,
          status: "failed",
          error: eventData.data?.message || "Analysis failed",
        }))
        break

      default:
        // Handle other event types like competitor-found, prompt-generated, etc.
        console.log(
          "[useRealTimeAnalysis] Unhandled event type:",
          eventData.type,
        )
        break
    }
  }

  // Stop polling
  function stopPolling() {
    if (pollingInterval) {
      clearInterval(pollingInterval)
      pollingInterval = null
      console.log("Stopped polling for analysis updates")
    }
  }

  // Cancel ongoing analysis
  function cancelAnalysis() {
    stopPolling()
    analysisState.update((state) => ({
      ...state,
      status: "idle",
      id: null,
      progress: null,
      result: null,
      error: null,
    }))
  }

  // Retry failed analysis
  async function retryAnalysis(analysisData: any, supabaseToken: string) {
    const currentState = analysisState.get()

    if (currentState.retryCount >= 3) {
      analysisState.update((state) => ({
        ...state,
        status: "failed",
        error: "Maximum retry attempts reached. Please try again later.",
      }))
      return
    }

    analysisState.update((state) => ({
      ...state,
      status: "retrying",
      retryCount: state.retryCount + 1,
      lastError: state.error,
      error: null,
    }))

    // Wait before retrying (exponential backoff)
    const delay = Math.min(1000 * Math.pow(2, currentState.retryCount), 10000)
    await new Promise((resolve) => setTimeout(resolve, delay))

    await startAnalysis(analysisData, supabaseToken)
  }

  // Reset state
  function reset() {
    stopPolling()
    analysisState.set({
      id: null,
      status: "idle",
      progress: null,
      result: null,
      error: null,
      retryCount: 0,
      lastError: null,
      connectionLost: false,
    })
  }

  // Cleanup function
  function destroy() {
    stopPolling()
  }

  return {
    // Stores
    analysisState,
    isAnalyzing,
    progressPercentage,
    currentStep,

    // Actions
    startAnalysis,
    retryAnalysis,
    cancelAnalysis,
    reset,
    destroy,
  }
}
