<script lang="ts">
  import { invalidate } from "$app/navigation"
  import { onMount } from "svelte"

  let { data, children } = $props()

  let { supabase, session } = data

  $effect(() => {
    ;({ supabase, session } = data)
  })

  onMount(() => {
    const { data } = supabase.auth.onAuthStateChange(
      async (event, _session) => {
        // Validate session changes by checking user authentication
        if (event === "SIGNED_IN" || event === "TOKEN_REFRESHED") {
          const {
            data: { user },
          } = await supabase.auth.getUser()
          if (user && _session?.expires_at !== session?.expires_at) {
            invalidate("supabase:auth")
          }
        } else if (event === "SIGNED_OUT") {
          invalidate("supabase:auth")
        }
      },
    )

    return () => data.subscription.unsubscribe()
  })
</script>

{@render children()}
