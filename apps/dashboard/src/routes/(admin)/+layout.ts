import { env } from "$env/dynamic/public"
import { createBrowserClient } from "@supabase/ssr"

import type { Database, Tables } from "$lib/supabase/supabase.types"

export const load = async ({ data, depends }) => {
  depends("supabase:auth")

  const supabase = createBrowserClient<Database>(
    env.PUBLIC_SUPABASE_URL!,
    env.PUBLIC_SUPABASE_ANON_KEY!,
  )

  // Note: Using getUser() for secure client-side authentication
  // This validates the JWT token with the Supabase Auth server
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser()

  // Get session only if user is authenticated
  let session = null
  if (user && !error) {
    const {
      data: { session: userSession },
    } = await supabase.auth.getSession()
    session = userSession
  }

  const profile: Tables<"profiles"> | null = data.profile

  return {
    ...data,
    supabase,
    session,
    profile,
  }
}

export const _hasFullProfile = (profile: Tables<"profiles"> | null) => {
  if (!profile) {
    return false
  }
  if (!profile.full_name) {
    return false
  }
  if (!profile.company_name) {
    return false
  }
  if (!profile.website) {
    return false
  }

  return true
}
