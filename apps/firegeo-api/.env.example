# Database
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"

# Application
NEXT_PUBLIC_APP_URL="http://localhost:3001"

# Email (Optional)
RESEND_API_KEY="re_..."

# AI Providers (Optional)
OPENAI_API_KEY="sk-..."
ANTHROPIC_API_KEY="sk-ant-..."
GOOGLE_GENERATIVE_AI_API_KEY="..."
PERPLEXITY_API_KEY="pplx-..."

# Firecrawl (Optional)
FIRECRAWL_API_KEY="fc-..."

# Integration with Dashboard (Supabase)
SUPABASE_URL="your_supabase_project_url"
SUPABASE_SERVICE_KEY="your_supabase_service_role_key"

# CORS Configuration
ALLOWED_ORIGINS="http://localhost:3000,https://yourdomain.com"
DASHBOARD_URL="http://localhost:3000"

# API Security
API_SECRET="your_shared_api_secret_key"

# Environment
NODE_ENV="development"
