import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type { UserProfile, UserSettings } from '@/lib/db/schema';

interface ProfileData {
  profile: UserProfile | null;
  settings: UserSettings | null;
  user: {
    id: string;
    email: string;
  };
}

export function useProfile() {
  // Always enabled since authentication is disabled
  return useQuery<ProfileData>({
    queryKey: ['profile', 'anonymous-user'],
    queryFn: async () => {
      const res = await fetch('/api/user/profile');
      if (!res.ok) {
        throw new Error('Failed to fetch profile');
      }
      return res.json();
    },
    enabled: true,
  });
}

export function useUpdateProfile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: Partial<UserProfile>) => {
      const res = await fetch('/api/user/profile', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!res.ok) {
        throw new Error('Failed to update profile');
      }

      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['profile', 'anonymous-user'] });
    },
  });
}

export function useSettings() {
  // Always enabled since authentication is disabled
  return useQuery<UserSettings>({
    queryKey: ['settings', 'anonymous-user'],
    queryFn: async () => {
      const res = await fetch('/api/user/settings');
      if (!res.ok) {
        throw new Error('Failed to fetch settings');
      }
      return res.json();
    },
    enabled: true,
  });
}

export function useUpdateSettings() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: Partial<UserSettings>) => {
      const res = await fetch('/api/user/settings', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!res.ok) {
        throw new Error('Failed to update settings');
      }

      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['settings', 'anonymous-user'] });
    },
  });
}