import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type { Conversation, Message } from '@/lib/db/schema';

interface ConversationWithLastMessage extends Conversation {
  messages: Message[];
}

export function useConversations() {
  // Always enabled since authentication is disabled
  return useQuery<ConversationWithLastMessage[]>({
    queryKey: ['conversations', 'anonymous-user'],
    queryFn: async () => {
      const res = await fetch('/api/chat');
      if (!res.ok) {
        throw new Error('Failed to fetch conversations');
      }
      return res.json();
    },
    enabled: true,
  });
}

export function useConversation(conversationId: string | null) {
  // Always enabled since authentication is disabled (only requires conversationId)
  return useQuery<Conversation & { messages: Message[] }>({
    queryKey: ['conversation', conversationId],
    queryFn: async () => {
      const res = await fetch(`/api/chat?conversationId=${conversationId}`);
      if (!res.ok) {
        throw new Error('Failed to fetch conversation');
      }
      return res.json();
    },
    enabled: !!conversationId,
  });
}

export function useDeleteConversation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (conversationId: string) => {
      const res = await fetch(`/api/chat/${conversationId}`, {
        method: 'DELETE',
      });

      if (!res.ok) {
        throw new Error('Failed to delete conversation');
      }

      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['conversations', 'anonymous-user'] });
    },
  });
}