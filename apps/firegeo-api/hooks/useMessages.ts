import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { API_ENDPOINTS, HTTP_METHODS, CONTENT_TYPES, ONE_MINUTE, CACHE_KEYS } from '@/config/constants';
import { parseApiResponse, ClientApiError } from '@/lib/client-errors';

interface SendMessageData {
  message: string;
  conversationId?: string;
}

interface MessageResponse {
  response: string;
  conversationId: string;
  messageId: string;
}

export function useSendMessage() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ message, conversationId }: SendMessageData) => {
      const res = await fetch(API_ENDPOINTS.CHAT, {
        method: HTTP_METHODS.POST,
        headers: {
          'Content-Type': CONTENT_TYPES.JSON,
        },
        body: JSON.stringify({ message, conversationId }),
      });

      return parseApiResponse<MessageResponse>(res);
    },
    onSuccess: (data) => {
      // Invalidate conversations list to update last message
      queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.CONVERSATIONS, 'anonymous-user'] });

      // Invalidate specific conversation to update messages
      if (data.conversationId) {
        queryClient.invalidateQueries({ queryKey: ['conversation', data.conversationId] });
      }
    },
  });
}

export function useMessageFeedback() {
  return useMutation({
    mutationFn: async ({ messageId, helpful }: { messageId: string; helpful: boolean }) => {
      const res = await fetch(API_ENDPOINTS.CHAT_FEEDBACK, {
        method: HTTP_METHODS.POST,
        headers: {
          'Content-Type': CONTENT_TYPES.JSON,
        },
        body: JSON.stringify({ messageId, helpful }),
      });
      
      return parseApiResponse(res);
    },
  });
}

// Credits system removed - unlimited access
export function useCredits() {
  return {
    data: { allowed: true, balance: 999999 },
    isLoading: false,
    error: null
  };
}