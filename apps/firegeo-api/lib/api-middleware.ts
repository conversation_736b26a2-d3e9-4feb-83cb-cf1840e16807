import { NextRequest, NextResponse } from 'next/server'
import { extractAuthContext } from './auth-bridge'
import type { ApiResponse } from 'shared'

/**
 * Middleware wrapper for API routes that require authentication
 */
export function withAuth(handler: (request: NextRequest, context: { userId: string, email: string }) => Promise<NextResponse>) {
  return async (request: NextRequest, routeParams?: any) => {
    try {
      const { authContext, isApiCall } = await extractAuthContext(request)
      
      if (!authContext) {
        const response: ApiResponse = {
          success: false,
          error: 'Unauthorized - valid authentication required'
        }
        return NextResponse.json(response, { status: 401 })
      }
      
      // Add user context to the request for the handler
      const context = {
        userId: authContext.userId,
        email: authContext.email,
        name: authContext.name,
        isApiCall
      }
      
      return await handler(request, context)
    } catch (error) {
      console.error('Auth middleware error:', error)
      
      const response: ApiResponse = {
        success: false,
        error: 'Authentication failed'
      }
      return NextResponse.json(response, { status: 500 })
    }
  }
}

/**
 * Middleware wrapper for API routes that don't require authentication
 */
export function withCors(handler: (request: NextRequest) => Promise<NextResponse>) {
  return async (request: NextRequest, routeParams?: any) => {
    try {
      return await handler(request)
    } catch (error) {
      console.error('API error:', error)
      
      const response: ApiResponse = {
        success: false,
        error: 'Internal server error'
      }
      return NextResponse.json(response, { status: 500 })
    }
  }
}

/**
 * Enhanced request object with user context
 */
export interface AuthenticatedRequest extends NextRequest {
  user: {
    userId: string
    email: string
    name?: string
  }
}

/**
 * Helper to create standardized API responses
 */
export function createApiResponse<T = any>(
  success: boolean,
  data?: T,
  error?: string,
  status: number = 200
): NextResponse {
  const response: ApiResponse<T> = {
    success,
    data,
    error
  }
  
  return NextResponse.json(response, { status })
}

/**
 * Helper to handle API errors consistently
 */
export function handleApiError(error: any, defaultMessage: string = 'An error occurred'): NextResponse {
  console.error('API Error:', error)
  
  const response: ApiResponse = {
    success: false,
    error: error.message || defaultMessage
  }
  
  const status = error.status || 500
  return NextResponse.json(response, { status })
}
