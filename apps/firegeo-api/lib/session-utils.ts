import { NextRequest } from 'next/server';
import { randomUUID } from 'crypto';

/**
 * Session-based user management for anonymous access
 * This replaces the authentication system with simple session tracking
 */

export interface SessionUser {
  id: string;
  email: string;
  name: string;
  sessionId: string;
  createdAt: Date;
}

/**
 * Generate a session ID for anonymous users
 */
export function generateSessionId(): string {
  return `session_${randomUUID()}`;
}

/**
 * Get or create a session user from request headers
 * This looks for a session ID in headers or creates a new anonymous session
 */
export function getSessionUser(request: NextRequest): SessionUser {
  // Try to get session ID from headers (set by client)
  const sessionId = request.headers.get('x-session-id') || generateSessionId();
  
  // Create anonymous user based on session
  return {
    id: `user_${sessionId}`,
    email: '<EMAIL>',
    name: 'Anonymous User',
    sessionId,
    createdAt: new Date()
  };
}

/**
 * Get a consistent user ID for database operations
 * This ensures data is tied to the session but remains anonymous
 */
export function getUserIdFromSession(sessionId?: string): string {
  if (sessionId) {
    return `user_${sessionId}`;
  }
  // Fallback to a default anonymous user for backward compatibility
  return 'anonymous-user';
}

/**
 * Create session headers for API responses
 * This allows the client to maintain session continuity
 */
export function createSessionHeaders(sessionId: string): Record<string, string> {
  return {
    'x-session-id': sessionId,
    'x-session-expires': new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
  };
}
