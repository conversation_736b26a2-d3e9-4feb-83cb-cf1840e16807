import { NextRequest } from 'next/server';
import { getSessionUser } from './session-utils';
import type { FireGeoAuthContext } from 'shared';

/**
 * Extract authentication context from request
 * This handles both API secret validation and session-based authentication
 */
export async function extractAuthContext(request: NextRequest): Promise<{
  authContext: FireGeoAuthContext | null;
  isApiCall: boolean;
}> {
  try {
    // Check for API secret header
    const apiSecret = request.headers.get('X-API-Secret');
    const expectedApiSecret = process.env.API_SECRET || 'RobynnDevSecretKey';
    
    // If API secret is provided and valid, allow access
    if (apiSecret && apiSecret === expectedApiSecret) {
      // Get session user for tracking
      const sessionUser = getSessionUser(request);
      
      return {
        authContext: {
          userId: sessionUser.id,
          email: sessionUser.email,
          name: sessionUser.name,
        },
        isApiCall: true,
      };
    }
    
    // Check for session-based authentication
    const sessionId = request.headers.get('x-session-id');
    if (sessionId) {
      const sessionUser = getSessionUser(request);
      
      return {
        authContext: {
          userId: sessionUser.id,
          email: sessionUser.email,
          name: sessionUser.name,
        },
        isApiCall: false,
      };
    }
    
    // No valid authentication found
    return {
      authContext: null,
      isApiCall: false,
    };
  } catch (error) {
    console.error('Auth context extraction failed:', error);
    return {
      authContext: null,
      isApiCall: false,
    };
  }
}

/**
 * Validate API secret from request headers
 */
export function validateApiSecret(request: NextRequest): boolean {
  const apiSecret = request.headers.get('X-API-Secret');
  const expectedApiSecret = process.env.API_SECRET || 'RobynnDevSecretKey';
  
  return apiSecret === expectedApiSecret;
}

/**
 * Create anonymous authentication context for endpoints that don't require auth
 */
export function createAnonymousAuthContext(request: NextRequest): FireGeoAuthContext {
  const sessionUser = getSessionUser(request);
  
  return {
    userId: sessionUser.id,
    email: sessionUser.email,
    name: sessionUser.name,
  };
}
