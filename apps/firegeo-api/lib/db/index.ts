import { createClient } from '@supabase/supabase-js';
import * as schema from './schema';

// Use Supabase connection for the database
const supabaseUrl = process.env.SUPABASE_URL || process.env.PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY!;

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Create a database wrapper that mimics <PERSON><PERSON><PERSON>'s query interface
export const db = {
  query: {
    brandAnalyses: {
      findMany: async (options: any) => {
        let query = supabase.from('brand_analyses').select('*');

        if (options.where) {
          // Handle where conditions - for now just handle userId
          if (options.where.sql && options.where.sql.includes('user_id')) {
            const userId = options.where.params?.[0] || 'test-user-id';
            query = query.eq('user_id', userId);
          }
        }

        if (options.orderBy) {
          // Handle order by - for now just handle created_at desc
          query = query.order('created_at', { ascending: false });
        }

        const { data, error } = await query;
        if (error) throw error;
        return data || [];
      },

      findFirst: async (options: any) => {
        let query = supabase.from('brand_analyses').select('*');

        if (options.where) {
          // Handle where conditions
          if (options.where.sql && options.where.sql.includes('id')) {
            const id = options.where.params?.[0];
            if (id) query = query.eq('id', id);
          }
          if (options.where.sql && options.where.sql.includes('user_id')) {
            const userId = options.where.params?.[1] || options.where.params?.[0];
            if (userId) query = query.eq('user_id', userId);
          }
        }

        const { data, error } = await query.limit(1).single();
        if (error && error.code !== 'PGRST116') throw error; // PGRST116 is "not found"
        return data;
      }
    }
  },

  insert: (table: any) => ({
    values: async (values: any) => {
      const { data, error } = await supabase.from('brand_analyses').insert(values).select();
      if (error) throw error;
      return data;
    },
    returning: () => ({
      values: async (values: any) => {
        const { data, error } = await supabase.from('brand_analyses').insert(values).select();
        if (error) throw error;
        return data;
      }
    })
  }),

  delete: (table: any) => ({
    where: (condition: any) => ({
      returning: async () => {
        // This is a simplified implementation - in a real app you'd parse the condition properly
        const { data, error } = await supabase.from('brand_analyses').delete().select();
        if (error) throw error;
        return data;
      }
    })
  })
};

// Export a client for compatibility
export const client = supabase;
