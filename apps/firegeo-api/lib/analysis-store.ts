// In-memory storage for analysis status (in production, use Redis or database)
export interface AnalysisStatus {
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  progress: {
    stage: string;
    percentage: number;
    message: string;
  };
  result?: any;
  error?: string;
  createdAt: Date;
  updatedAt: Date;
  company?: {
    name: string;
    url?: string;
    industry?: string;
  };
  prompts?: string[];
  competitors?: string[];
}

export const analysisStore = new Map<string, AnalysisStatus>();

export function createAnalysis(
  id: string, 
  company: { name: string; url?: string; industry?: string },
  prompts?: string[],
  competitors?: string[]
): AnalysisStatus {
  const analysis: AnalysisStatus = {
    status: 'pending',
    progress: {
      stage: 'queued',
      percentage: 0,
      message: 'Analysis queued for processing'
    },
    createdAt: new Date(),
    updatedAt: new Date(),
    company,
    prompts,
    competitors
  };
  
  analysisStore.set(id, analysis);
  return analysis;
}

export function updateAnalysisProgress(
  id: string,
  stage: string,
  percentage: number,
  message: string
): void {
  const analysis = analysisStore.get(id);
  if (analysis) {
    analysis.progress = { stage, percentage, message };
    analysis.updatedAt = new Date();
    if (percentage > 0) {
      analysis.status = 'in_progress';
    }
    analysisStore.set(id, analysis);
  }
}

export function completeAnalysis(id: string, result: any): void {
  const analysis = analysisStore.get(id);
  if (analysis) {
    analysis.status = 'completed';
    analysis.progress = {
      stage: 'completed',
      percentage: 100,
      message: 'Analysis completed successfully'
    };
    analysis.result = result;
    analysis.updatedAt = new Date();
    analysisStore.set(id, analysis);
  }
}

export function failAnalysis(id: string, error: string): void {
  const analysis = analysisStore.get(id);
  if (analysis) {
    analysis.status = 'failed';
    analysis.error = error;
    analysis.progress = {
      stage: 'error',
      percentage: 0,
      message: error
    };
    analysis.updatedAt = new Date();
    analysisStore.set(id, analysis);
  }
}

export function getAnalysis(id: string): AnalysisStatus | undefined {
  return analysisStore.get(id);
}
