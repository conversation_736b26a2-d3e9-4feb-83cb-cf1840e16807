import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// Create Supabase client with service role key for server-side operations
const supabase = createClient(supabaseUrl, supabaseServiceKey);

export interface AnalysisSession {
  id: string;
  user_id: string;
  url: string;
  company_name?: string;
  industry?: string;
  competitors?: string[];
  prompts?: string[];
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  progress_stage: string;
  progress_percentage: number;
  progress_message: string;
  result_data?: any;
  error_message?: string;
  created_at: string;
  updated_at: string;
}

export async function createAnalysisSession(
  userId: string,
  url: string,
  companyName?: string,
  industry?: string,
  competitors?: string[],
  prompts?: string[]
): Promise<AnalysisSession> {
  const { data, error } = await supabase
    .from('brand_analysis_sessions')
    .insert({
      user_id: userId,
      url,
      company_name: companyName,
      industry,
      competitors,
      prompts,
      status: 'pending',
      progress_stage: 'queued',
      progress_percentage: 0,
      progress_message: 'Analysis queued for processing'
    })
    .select()
    .single();

  if (error) {
    console.error('Failed to create analysis session:', error);
    throw new Error(`Failed to create analysis session: ${error.message}`);
  }

  return data;
}

export async function updateAnalysisProgress(
  sessionId: string,
  stage: string,
  percentage: number,
  message: string
): Promise<void> {
  const { error } = await supabase
    .from('brand_analysis_sessions')
    .update({
      status: percentage > 0 ? 'in_progress' : 'pending',
      progress_stage: stage,
      progress_percentage: percentage,
      progress_message: message
    })
    .eq('id', sessionId);

  if (error) {
    console.error('Failed to update analysis progress:', error);
    throw new Error(`Failed to update analysis progress: ${error.message}`);
  }
}

export async function completeAnalysisSession(
  sessionId: string,
  resultData: any
): Promise<void> {
  const { error } = await supabase
    .from('brand_analysis_sessions')
    .update({
      status: 'completed',
      progress_stage: 'completed',
      progress_percentage: 100,
      progress_message: 'Analysis completed successfully',
      result_data: resultData
    })
    .eq('id', sessionId);

  if (error) {
    console.error('Failed to complete analysis session:', error);
    throw new Error(`Failed to complete analysis session: ${error.message}`);
  }
}

export async function failAnalysisSession(
  sessionId: string,
  errorMessage: string
): Promise<void> {
  const { error } = await supabase
    .from('brand_analysis_sessions')
    .update({
      status: 'failed',
      progress_stage: 'error',
      progress_percentage: 0,
      progress_message: errorMessage,
      error_message: errorMessage
    })
    .eq('id', sessionId);

  if (error) {
    console.error('Failed to fail analysis session:', error);
    throw new Error(`Failed to fail analysis session: ${error.message}`);
  }
}

export async function getAnalysisSession(sessionId: string): Promise<AnalysisSession | null> {
  const { data, error } = await supabase
    .from('brand_analysis_sessions')
    .select('*')
    .eq('id', sessionId)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      // No rows returned
      return null;
    }
    console.error('Failed to get analysis session:', error);
    throw new Error(`Failed to get analysis session: ${error.message}`);
  }

  return data;
}

export async function getUserAnalysisSessions(userId: string): Promise<AnalysisSession[]> {
  const { data, error } = await supabase
    .from('brand_analysis_sessions')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Failed to get user analysis sessions:', error);
    throw new Error(`Failed to get user analysis sessions: ${error.message}`);
  }

  return data || [];
}

// Function to save completed analysis to the main brand_analyses table
export async function saveCompletedAnalysis(session: AnalysisSession): Promise<void> {
  if (session.status !== 'completed' || !session.result_data) {
    throw new Error('Analysis session is not completed or has no result data');
  }

  const { error } = await supabase
    .from('brand_analyses')
    .insert({
      user_id: session.user_id,
      url: session.url,
      company_name: session.company_name,
      industry: session.industry,
      analysis_data: session.result_data,
      competitors: session.competitors,
      prompts: session.prompts
    });

  if (error) {
    console.error('Failed to save completed analysis:', error);
    throw new Error(`Failed to save completed analysis: ${error.message}`);
  }
}
