import { NextRequest, NextResponse } from 'next/server';
import { getSessionUser, createSessionHeaders } from '@/lib/session-utils';
import { handleApiError } from '@/lib/api-errors';

/**
 * Authentication bridge endpoint for anonymous access
 * This replaces the previous Supabase authentication with session-based anonymous access
 */

// POST /api/auth/bridge - Authenticate user (now anonymous session-based)
export async function POST(request: NextRequest) {
  try {
    // Get session user (anonymous but with session tracking)
    const sessionUser = getSessionUser(request);
    
    console.log('Auth Bridge - Session User:', sessionUser.id);

    // Return successful authentication response
    const authResponse = {
      success: true,
      session: {
        userId: sessionUser.id,
        email: sessionUser.email,
        name: sessionUser.name,
        sessionId: sessionUser.sessionId,
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
      },
      user: {
        id: sessionUser.id,
        email: sessionUser.email,
        name: sessionUser.name,
      }
    };

    const response = NextResponse.json(authResponse);

    // Add session headers
    const sessionHeaders = createSessionHeaders(sessionUser.sessionId);
    Object.entries(sessionHeaders).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;
  } catch (error) {
    console.error('Auth Bridge error:', error);
    return handleApiError(error);
  }
}

// GET /api/auth/bridge - Health check endpoint
export async function GET(request: NextRequest) {
  try {
    // Simple health check for the FireGeo API
    return NextResponse.json({
      success: true,
      status: 'healthy',
      service: 'FireGeo API',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    });
  } catch (error) {
    console.error('Auth Bridge health check error:', error);
    return handleApiError(error);
  }
}
