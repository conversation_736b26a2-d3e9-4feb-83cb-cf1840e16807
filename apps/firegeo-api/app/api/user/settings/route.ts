import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { userSettings } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { handleApiError, ValidationError } from '@/lib/api-errors';

// GET /api/user/settings - Get user settings
export async function GET(request: NextRequest) {
  try {
    // Use anonymous session for now
    const mockUserId = 'anonymous-user';

    const settings = await db.query.userSettings.findFirst({
      where: eq(userSettings.userId, mockUserId),
    });

    // Return default settings if none exist
    if (!settings) {
      return NextResponse.json({
        theme: 'light',
        emailNotifications: true,
        marketingEmails: false,
        defaultModel: 'gpt-3.5-turbo',
        metadata: {},
      });
    }

    return NextResponse.json(settings);
  } catch (error) {
    return handleApiError(error);
  }
}

// PUT /api/user/settings - Update user settings
export async function PUT(request: NextRequest) {
  try {
    // TEMPORARILY DISABLED: Authentication for testing
    console.log('[User Settings API] Authentication temporarily disabled for testing');

    const data = await request.json();
    const { theme, emailNotifications, marketingEmails, defaultModel, metadata } = data;

    // Validate theme
    if (theme && !['light', 'dark'].includes(theme)) {
      return NextResponse.json({ error: 'Invalid theme value' }, { status: 400 });
    }

    // Return mock settings (no database operation)
    const mockUpdatedSettings = {
      id: 'anonymous-settings',
      userId: 'anonymous-user',
      theme: theme || 'light',
      emailNotifications: emailNotifications ?? true,
      marketingEmails: marketingEmails ?? false,
      defaultModel: defaultModel || 'gpt-3.5-turbo',
      metadata: metadata || {},
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    return NextResponse.json(mockUpdatedSettings);
  } catch (error) {
    return handleApiError(error);
  }
}

// PATCH /api/user/settings - Partial update
export async function PATCH(request: NextRequest) {
  try {
    // TEMPORARILY DISABLED: Authentication for testing
    console.log('[User Settings PATCH API] Authentication temporarily disabled for testing');

    const updates = await request.json();

    // Remove any fields that shouldn't be updated
    delete updates.id;
    delete updates.userId;
    delete updates.createdAt;

    // Validate theme if provided
    if (updates.theme && !['light', 'dark'].includes(updates.theme)) {
      return NextResponse.json({ error: 'Invalid theme value' }, { status: 400 });
    }

    if (Object.keys(updates).length === 0) {
      return NextResponse.json({ error: 'No valid fields to update' }, { status: 400 });
    }

    // Return mock updated settings (no database operation)
    const mockUpdatedSettings = {
      id: 'anonymous-settings',
      userId: 'anonymous-user',
      theme: 'light',
      emailNotifications: true,
      marketingEmails: false,
      defaultModel: 'gpt-3.5-turbo',
      metadata: {},
      ...updates,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    return NextResponse.json(mockUpdatedSettings);
  } catch (error) {
    return handleApiError(error);
  }
}