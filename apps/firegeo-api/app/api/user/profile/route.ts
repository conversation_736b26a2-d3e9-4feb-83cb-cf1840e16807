import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { userProfile, userSettings } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { handleApiError, AuthenticationError, ValidationError } from '@/lib/api-errors';

// GET /api/user/profile - Get user profile and settings
export async function GET(request: NextRequest) {
  try {
    // TEMPORARILY DISABLED: Authentication for testing
    console.log('[User Profile API] Authentication temporarily disabled for testing');

    // Return mock profile data for anonymous access
    return NextResponse.json({
      profile: {
        displayName: 'Anonymous User',
        bio: 'Anonymous user profile',
        phone: null,
      },
      settings: {
        theme: 'light',
        emailNotifications: true,
        marketingEmails: false,
        defaultModel: 'gpt-4',
      },
      user: {
        id: 'anonymous-user',
        email: '<EMAIL>',
      },
    });
  } catch (error) {
    return handleApiError(error);
  }
}

// PUT /api/user/profile - Update user profile
export async function PUT(request: NextRequest) {
  try {
    // TEMPORARILY DISABLED: Authentication for testing
    console.log('[User Profile PUT API] Authentication temporarily disabled for testing');

    const data = await request.json();
    const { displayName, avatarUrl, bio, phone } = data;

    // Return mock updated profile (no database operation)
    const mockUpdatedProfile = {
      id: 'anonymous-profile',
      userId: 'anonymous-user',
      displayName: displayName || 'Anonymous User',
      avatarUrl: avatarUrl || null,
      bio: bio || 'Anonymous user profile',
      phone: phone || null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    return NextResponse.json(mockUpdatedProfile);
  } catch (error) {
    return handleApiError(error);
  }
}

// PATCH /api/user/profile - Partial update
export async function PATCH(request: NextRequest) {
  try {
    // TEMPORARILY DISABLED: Authentication for testing
    console.log('[User Profile PATCH API] Authentication temporarily disabled for testing');

    const updates = await request.json();

    // Remove any fields that shouldn't be updated
    delete updates.id;
    delete updates.userId;
    delete updates.createdAt;

    if (Object.keys(updates).length === 0) {
      return NextResponse.json({ error: 'No valid fields to update' }, { status: 400 });
    }

    // Return mock updated profile (no database operation)
    const mockUpdatedProfile = {
      id: 'anonymous-profile',
      userId: 'anonymous-user',
      displayName: 'Anonymous User',
      avatarUrl: null,
      bio: 'Anonymous user profile',
      phone: null,
      ...updates,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    return NextResponse.json(mockUpdatedProfile);
  } catch (error) {
    return handleApiError(error);
  }
}