import { NextRequest, NextResponse } from 'next/server';
import {
  handleApiError,
  ValidationError,
  ExternalServiceError
} from '@/lib/api-errors';
import { ERROR_MESSAGES } from '@/config/constants';
import {
  createAnalysisSession,
  updateAnalysisProgress,
  completeAnalysisSession,
  failAnalysisSession,
  saveCompletedAnalysis
} from '@/lib/supabase-analysis';

export async function POST(request: NextRequest) {
  try {
    // Anonymous access - no authentication required
    const requestBody = await request.json();
    const { company, prompts: customPrompts, competitors: userSelectedCompetitors, useWebSearch = false } = requestBody;

    // DEBUG: Log the received request body
    console.log('[FireGeo API] Received start-analysis request:', JSON.stringify(requestBody, null, 2));
    console.log('[FireGeo API] Parsed company:', company);
    console.log('[FireGeo API] Company name:', company?.name);

    if (!company || !company.name) {
      console.error('[FireGeo API] Validation failed - missing company info:', { company });
      throw new ValidationError(ERROR_MESSAGES.COMPANY_INFO_REQUIRED, {
        company: 'Company name is required'
      });
    }

    // Create analysis session in Supabase
    const session = await createAnalysisSession(
      'anonymous-user', // For now, using anonymous user
      company.url || '',
      company.name,
      company.industry,
      userSelectedCompetitors,
      customPrompts
    );

    // Start background processing
    startBackgroundAnalysis(session.id);

    const analysisResponse = {
      id: session.id,
      status: session.status,
      company: {
        name: session.company_name,
        url: session.url,
        industry: session.industry
      },
      createdAt: session.created_at,
      updatedAt: session.updated_at,
      progress: {
        stage: session.progress_stage,
        percentage: session.progress_percentage,
        message: session.progress_message
      }
    };

    console.log('[FireGeo API] Analysis started with ID:', session.id);

    return NextResponse.json(analysisResponse);
  } catch (error) {
    return handleApiError(error);
  }
}

// Background analysis function
async function startBackgroundAnalysis(sessionId: string) {
  try {
    // Simulate analysis progress
    setTimeout(() => updateAnalysisProgress(sessionId, 'initializing', 10, 'Initializing analysis...'), 1000);
    setTimeout(() => updateAnalysisProgress(sessionId, 'scraping', 25, 'Scraping website content...'), 3000);
    setTimeout(() => updateAnalysisProgress(sessionId, 'analyzing', 50, 'Analyzing with AI models...'), 6000);
    setTimeout(() => updateAnalysisProgress(sessionId, 'processing', 75, 'Processing results...'), 9000);

    // Complete after 12 seconds with mock data
    setTimeout(async () => {
      try {
        const mockResult = {
          summary: 'Analysis completed successfully',
          insights: [
            'Strong brand presence detected',
            'Positive sentiment across AI models',
            'Competitive positioning identified'
          ],
          recommendations: [
            'Enhance content marketing strategy',
            'Improve SEO optimization',
            'Expand social media presence'
          ]
        };

        await completeAnalysisSession(sessionId, mockResult);

        // Optionally save to main brand_analyses table
        // const session = await getAnalysisSession(sessionId);
        // if (session) {
        //   await saveCompletedAnalysis(session);
        // }
      } catch (error) {
        console.error('Failed to complete analysis:', error);
        await failAnalysisSession(sessionId, error instanceof Error ? error.message : 'Analysis completion failed');
      }
    }, 12000);

  } catch (error) {
    console.error('Background analysis failed:', error);
    await failAnalysisSession(sessionId, error instanceof Error ? error.message : 'Analysis failed');
  }
}
