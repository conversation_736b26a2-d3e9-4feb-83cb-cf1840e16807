import { NextRequest, NextResponse } from 'next/server';
import { scrapeCompanyInfo } from '@/lib/scrape-utils';
import {
  handleApiError,
  ValidationError,
  ExternalServiceError
} from '@/lib/api-errors';

export async function POST(request: NextRequest) {
  try {
    // Anonymous access - no authentication required
    const { url, maxAge } = await request.json();

    if (!url) {
      throw new ValidationError('Invalid request', {
        url: 'URL is required'
      });
    }

    // Ensure URL has protocol
    let normalizedUrl = url.trim();
    if (!normalizedUrl.startsWith('http://') && !normalizedUrl.startsWith('https://')) {
      normalizedUrl = `https://${normalizedUrl}`;
    }

    const company = await scrapeCompanyInfo(normalizedUrl, maxAge);

    return NextResponse.json({ company });
  } catch (error) {
    return handleApiError(error);
  }
}