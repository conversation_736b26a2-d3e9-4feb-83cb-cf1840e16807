import { NextRequest, NextResponse } from 'next/server';
import { scrapeCompanyInfo } from '@/lib/scrape-utils';
import {
  handleApiError,
  ValidationError,
  ExternalServiceError
} from '@/lib/api-errors';
import { ERROR_MESSAGES } from '@/config/constants';

export async function POST(request: NextRequest) {
  try {
    // Anonymous access - no authentication required
    const { url, companyName } = await request.json();

    if (!url || !companyName) {
      throw new ValidationError('Invalid request', {
        url: !url ? 'URL is required' : undefined,
        companyName: !companyName ? 'Company name is required' : undefined
      });
    }

    // Ensure URL has protocol
    let normalizedUrl = url.trim();
    if (!normalizedUrl.startsWith('http://') && !normalizedUrl.startsWith('https://')) {
      normalizedUrl = `https://${normalizedUrl}`;
    }

    console.log('[FireGeo API] Starting company discovery for:', companyName, 'at', normalizedUrl);

    // Step 1: Scrape company information from website
    const company = await scrapeCompanyInfo(normalizedUrl);
    
    // Override with user-provided company name
    company.name = companyName;

    // Step 2: Generate analysis prompts based on company info
    const generatedPrompts = generateAnalysisPrompts(company);

    // Step 3: Suggest competitors based on industry/domain
    const suggestedCompetitors = await suggestCompetitors(company);

    const discoveryResult = {
      company: {
        name: company.name,
        url: normalizedUrl,
        industry: company.industry,
        description: company.description,
        logo: company.logo,
        keywords: company.keywords || []
      },
      generatedPrompts,
      suggestedCompetitors,
      extractedAt: new Date().toISOString()
    };

    console.log('[FireGeo API] Company discovery completed for:', companyName);

    return NextResponse.json(discoveryResult);
  } catch (error) {
    console.error('[FireGeo API] Company discovery failed:', error);
    return handleApiError(error);
  }
}

// Generate analysis prompts tailored to the company's industry/domain
function generateAnalysisPrompts(company: any): string[] {
  const serviceType = detectServiceType(company);
  const currentYear = new Date().getFullYear();
  
  const basePrompts = [
    `Best ${serviceType}s in ${currentYear}?`,
    `Top ${serviceType}s for startups?`,
    `Most popular ${serviceType}s today?`,
    `Recommended ${serviceType}s for developers?`
  ];

  // Industry-specific prompts
  const industryPrompts: Record<string, string[]> = {
    'technology': [
      `Leading tech companies for ${serviceType} solutions?`,
      `Enterprise ${serviceType} platforms comparison?`,
      `Open source alternatives to ${company.name}?`
    ],
    'saas': [
      `Best SaaS ${serviceType} tools?`,
      `${company.name} vs competitors pricing?`,
      `Cloud-based ${serviceType} solutions?`
    ],
    'ecommerce': [
      `Top e-commerce ${serviceType} platforms?`,
      `${company.name} for online stores?`,
      `E-commerce ${serviceType} integrations?`
    ],
    'fintech': [
      `Financial ${serviceType} solutions?`,
      `Banking ${serviceType} platforms?`,
      `Fintech ${serviceType} comparison?`
    ],
    'healthcare': [
      `Healthcare ${serviceType} systems?`,
      `Medical ${serviceType} platforms?`,
      `HIPAA compliant ${serviceType} tools?`
    ]
  };

  const industry = company.industry?.toLowerCase() || 'technology';
  const specificPrompts = industryPrompts[industry] || industryPrompts['technology'];

  return [...basePrompts, ...specificPrompts].slice(0, 8); // Limit to 8 prompts
}

// Suggest competitors based on company information
async function suggestCompetitors(company: any): Promise<string[]> {
  const serviceType = detectServiceType(company);
  const industry = company.industry?.toLowerCase() || 'technology';

  // Industry-specific competitor suggestions
  const competitorSuggestions: Record<string, string[]> = {
    'technology': ['Microsoft', 'Google', 'Amazon', 'Apple', 'Meta'],
    'saas': ['Salesforce', 'HubSpot', 'Slack', 'Zoom', 'Atlassian'],
    'ecommerce': ['Shopify', 'WooCommerce', 'Magento', 'BigCommerce', 'Squarespace'],
    'fintech': ['Stripe', 'PayPal', 'Square', 'Plaid', 'Robinhood'],
    'healthcare': ['Epic', 'Cerner', 'Allscripts', 'athenahealth', 'Veracyte'],
    'analytics': ['Tableau', 'Power BI', 'Looker', 'Qlik', 'Sisense'],
    'crm': ['Salesforce', 'HubSpot', 'Pipedrive', 'Zoho', 'Monday.com'],
    'communication': ['Slack', 'Microsoft Teams', 'Discord', 'Zoom', 'Webex']
  };

  // Try to match by service type first, then by industry
  let suggestions = competitorSuggestions[serviceType.toLowerCase()] || 
                   competitorSuggestions[industry] || 
                   competitorSuggestions['technology'];

  // Filter out the company itself if it appears in suggestions
  suggestions = suggestions.filter(comp => 
    comp.toLowerCase() !== company.name.toLowerCase()
  );

  return suggestions.slice(0, 5); // Limit to 5 suggestions
}

// Detect service type from company information
function detectServiceType(company: any): string {
  const name = company.name?.toLowerCase() || '';
  const description = company.description?.toLowerCase() || '';
  const keywords = company.keywords?.join(' ').toLowerCase() || '';
  const combined = `${name} ${description} ${keywords}`;

  // Service type detection patterns
  const patterns: Record<string, string[]> = {
    'CRM': ['crm', 'customer relationship', 'sales management', 'lead management'],
    'Analytics': ['analytics', 'data analysis', 'business intelligence', 'reporting'],
    'Communication': ['chat', 'messaging', 'communication', 'collaboration'],
    'E-commerce': ['ecommerce', 'online store', 'shopping cart', 'marketplace'],
    'Project Management': ['project management', 'task management', 'workflow'],
    'Marketing': ['marketing', 'email marketing', 'social media', 'advertising'],
    'Development': ['development', 'coding', 'programming', 'software'],
    'Design': ['design', 'graphics', 'ui/ux', 'creative'],
    'Finance': ['finance', 'accounting', 'payment', 'billing'],
    'HR': ['human resources', 'hr', 'recruitment', 'payroll']
  };

  for (const [serviceType, keywords] of Object.entries(patterns)) {
    if (keywords.some(keyword => combined.includes(keyword))) {
      return serviceType;
    }
  }

  return 'Software'; // Default fallback
}
