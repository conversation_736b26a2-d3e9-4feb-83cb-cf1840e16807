import { NextRequest } from 'next/server';
import { createSSEMessage } from '@/lib/analyze-common';
import { SSEEvent } from '@/lib/types';
import {
  ValidationError,
  handleApiError
} from '@/lib/api-errors';
import {
  ERROR_MESSAGES
} from '@/config/constants';
import { getAnalysisSession } from '@/lib/supabase-analysis';

export const runtime = 'nodejs';
export const maxDuration = 300; // 5 minutes

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const analysisId = searchParams.get('id');
    const token = searchParams.get('token');

    console.log('[FireGeo API] SSE status request for analysis:', analysisId);

    if (!analysisId) {
      throw new ValidationError('Analysis ID is required', {
        id: 'Analysis ID parameter is required'
      });
    }

    // Create a TransformStream for SSE
    const encoder = new TextEncoder();
    const stream = new TransformStream();
    const writer = stream.writable.getWriter();

    // Function to send SSE events
    const sendEvent = async (event: SSEEvent) => {
      await writer.write(encoder.encode(createSSEMessage(event)));
    };

    // Start the status monitoring
    (async () => {
      try {
        let checkCount = 0;
        const maxChecks = 300; // 5 minutes with 1-second intervals
        
        const statusInterval = setInterval(async () => {
          checkCount++;

          try {
            // Get analysis status from Supabase
            const session = await getAnalysisSession(analysisId);

            if (!session) {
              // Analysis not found, send error and close
              await sendEvent({
                type: 'error',
                stage: 'error',
                data: {
                  message: 'Analysis not found'
                },
                timestamp: new Date()
              });
              clearInterval(statusInterval);
              await writer.close();
              return;
            }

            // Send progress update
            await sendEvent({
              type: 'progress',
              stage: session.progress_stage,
              data: {
                progress: session.progress_percentage,
                step: session.progress_message,
                status: session.status
              },
              timestamp: new Date()
            });

            // Check if analysis is complete
            if (session.status === 'completed') {
              await sendEvent({
                type: 'complete',
                stage: 'finalizing',
                data: {
                  analysis: session.result_data
                },
                timestamp: new Date()
              });
              clearInterval(statusInterval);
              await writer.close();
              return;
            }

            // Check if analysis failed
            if (session.status === 'failed') {
              await sendEvent({
                type: 'error',
                stage: 'error',
                data: {
                  message: session.error_message || 'Analysis failed'
                },
                timestamp: new Date()
              });
              clearInterval(statusInterval);
              await writer.close();
              return;
            }
          } catch (error) {
            console.error('Error fetching analysis session:', error);
            await sendEvent({
              type: 'error',
              stage: 'error',
              data: {
                message: 'Failed to fetch analysis status'
              },
              timestamp: new Date()
            });
            clearInterval(statusInterval);
            await writer.close();
            return;
          }

          // Timeout check
          if (checkCount >= maxChecks) {
            await sendEvent({
              type: 'error',
              stage: 'error',
              data: {
                message: 'Analysis timeout'
              },
              timestamp: new Date()
            });
            clearInterval(statusInterval);
            await writer.close();
            return;
          }
        }, 1000); // Check every second

        // Clean up on client disconnect
        request.signal.addEventListener('abort', () => {
          clearInterval(statusInterval);
          writer.close();
        });

      } catch (error) {
        console.error('Status monitoring error:', error);
        await sendEvent({
          type: 'error',
          stage: 'error',
          data: {
            message: error instanceof Error ? error.message : 'Status monitoring failed'
          },
          timestamp: new Date()
        });
        await writer.close();
      }
    })();

    return new Response(stream.readable, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': 'http://localhost:3000',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Secret',
      },
    });

  } catch (error) {
    console.error('Brand monitor status API error:', error);
    return handleApiError(error);
  }
}


