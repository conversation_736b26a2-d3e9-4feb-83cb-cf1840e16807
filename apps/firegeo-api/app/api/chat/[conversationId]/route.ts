import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { conversations } from '@/lib/db/schema';
import { and, eq } from 'drizzle-orm';
import { handleApiError, NotFoundError } from '@/lib/api-errors';

// DELETE /api/chat/[conversationId] - Delete a conversation
export async function DELETE(
  request: NextRequest,
  { params }: { params: { conversationId: string } }
) {
  try {
    // Use anonymous session for now - in a real implementation, you'd use session-based user identification
    const mockUserId = 'anonymous-user';

    const { conversationId } = params;

    // Verify the conversation exists
    const conversation = await db.query.conversations.findFirst({
      where: and(
        eq(conversations.id, conversationId),
        eq(conversations.userId, mockUserId)
      ),
    });

    if (!conversation) {
      throw new NotFoundError('Conversation');
    }

    // Delete the conversation (messages will cascade delete)
    await db
      .delete(conversations)
      .where(
        and(
          eq(conversations.id, conversationId),
          eq(conversations.userId, mockUserId)
        )
      );

    return NextResponse.json({ success: true });
  } catch (error) {
    return handleApiError(error);
  }
}