import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { conversations, messages } from '@/lib/db/schema';
import { eq, and, desc } from 'drizzle-orm';
import {
  ValidationError,
  DatabaseError,
  ExternalServiceError,
  handleApiError
} from '@/lib/api-errors';
import {
  ERROR_MESSAGES,
  ROLE_USER,
  ROLE_ASSISTANT,
  UI_LIMITS
} from '@/config/constants';
import { getSessionUser, createSessionHeaders } from '@/lib/session-utils';

export async function POST(request: NextRequest) {
  try {
    // Get session user (anonymous but with session tracking)
    const sessionUser = getSessionUser(request);

    console.log('Chat API - Session User:', sessionUser.id);

    const { message, conversationId } = await request.json();

    if (!message || typeof message !== 'string') {
      throw new ValidationError('Invalid message format', {
        message: 'Message must be a non-empty string'
      });
    }

    // No credit checking needed - unlimited access

    // Get or create conversation
    let currentConversation;
    
    if (conversationId) {
      // Find existing conversation
      const existingConversation = await db.query.conversations.findFirst({
        where: and(
          eq(conversations.id, conversationId),
          eq(conversations.userId, sessionUser.id)
        ),
      });
      
      if (existingConversation) {
        currentConversation = existingConversation;
        // Update last message timestamp
        await db
          .update(conversations)
          .set({ lastMessageAt: new Date() })
          .where(eq(conversations.id, conversationId));
      }
    }
    
    if (!currentConversation) {
      // Create new conversation
      const [newConversation] = await db
        .insert(conversations)
        .values({
          userId: sessionUser.id,
          title: message.substring(0, UI_LIMITS.TITLE_MAX_LENGTH) + (message.length > UI_LIMITS.TITLE_MAX_LENGTH ? '...' : ''),
          lastMessageAt: new Date(),
        })
        .returning();
      
      currentConversation = newConversation;
    }

    // Store user message
    const [userMessage] = await db
      .insert(messages)
      .values({
        conversationId: currentConversation.id,
        userId: sessionUser.id,
        role: ROLE_USER,
        content: message,
      })
      .returning();

    // Simple mock AI response
    const responses = [
      "I understand you're asking about " + message.substring(0, 20) + ". Here's what I think...",
      "That's an interesting question! Let me help you with that.",
      "Based on what you're saying, I can suggest the following approach...",
      "Thanks for your message! Here's my response to your query.",
      "I'm here to help! Regarding your question about " + message.substring(0, 15) + "...",
    ];

    const randomResponse = responses[Math.floor(Math.random() * responses.length)];

    // Store AI response
    const [aiMessage] = await db
      .insert(messages)
      .values({
        conversationId: currentConversation.id,
        userId: sessionUser.id,
        role: ROLE_ASSISTANT,
        content: randomResponse,
        tokenCount: randomResponse.length, // Simple token count estimate
      })
      .returning();

    const response = NextResponse.json({
      response: randomResponse,
      conversationId: currentConversation.id,
      messageId: aiMessage.id,
    });

    // Add session headers
    const sessionHeaders = createSessionHeaders(sessionUser.sessionId);
    Object.entries(sessionHeaders).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;
  } catch (error: any) {
    console.error('Chat API error:', error);
    return handleApiError(error);
  }
}

// GET endpoint to fetch conversation history
export async function GET(request: NextRequest) {
  try {
    // Get session user (anonymous but with session tracking)
    const sessionUser = getSessionUser(request);

    const { searchParams } = new URL(request.url);
    const conversationId = searchParams.get('conversationId');

    if (conversationId) {
      // Get specific conversation with messages
      const conversation = await db.query.conversations.findFirst({
        where: and(
          eq(conversations.id, conversationId),
          eq(conversations.userId, sessionUser.id)
        ),
        with: {
          messages: {
            orderBy: [messages.createdAt],
          },
        },
      });

      if (!conversation) {
        return NextResponse.json({ error: 'Conversation not found' }, { status: 404 });
      }

      return NextResponse.json(conversation);
    } else {
      // Get all conversations for the user
      const userConversations = await db.query.conversations.findMany({
        where: eq(conversations.userId, sessionUser.id),
        orderBy: [desc(conversations.lastMessageAt)],
        with: {
          messages: {
            limit: 1,
            orderBy: [desc(messages.createdAt)],
          },
        },
      });

      return NextResponse.json(userConversations);
    }
  } catch (error: any) {
    console.error('Chat GET error:', error);
    return handleApiError(error);
  }
}
