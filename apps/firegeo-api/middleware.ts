import { NextRequest, NextResponse } from 'next/server';

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // Handle CORS for API routes
  if (pathname.startsWith('/api/')) {
    const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || []
    const origin = request.headers.get('origin')
    const isDevelopment = process.env.NODE_ENV === 'development'

    // In development, allow localhost origins even if not in ALLOWED_ORIGINS
    const isAllowedOrigin = origin && (
      allowedOrigins.includes(origin) ||
      (isDevelopment && origin.startsWith('http://localhost'))
    )

    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      return new NextResponse(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': isAllowedOrigin ? origin : '',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Secret, X-User-ID, X-User-Email, X-User-Name, X-Session-ID',
          'Access-Control-Allow-Credentials': 'true',
          'Access-Control-Max-Age': '86400',
        },
      })
    }
  }

  const response = NextResponse.next();

  // Add CORS headers for API routes
  if (pathname.startsWith('/api/')) {
    const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || []
    const origin = request.headers.get('origin')
    const isDevelopment = process.env.NODE_ENV === 'development'

    // In development, allow localhost origins even if not in ALLOWED_ORIGINS
    const isAllowedOrigin = origin && (
      allowedOrigins.includes(origin) ||
      (isDevelopment && origin.startsWith('http://localhost'))
    )

    if (isAllowedOrigin) {
      response.headers.set('Access-Control-Allow-Origin', origin)
      response.headers.set('Access-Control-Allow-Credentials', 'true')
      response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
      response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-API-Secret, X-User-ID, X-User-Email, X-User-Name, X-Session-ID')
    }
  }

  // Security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all API routes explicitly
     */
    '/api/:path*',
  ],
};
