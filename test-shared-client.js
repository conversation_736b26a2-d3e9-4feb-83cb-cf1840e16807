#!/usr/bin/env node

// Test the shared FireGeo client exactly as the dashboard uses it
const { FireGeoApiClient } = require('./packages/shared/dist/api-client/firegeo-client.js');

async function testSharedClient() {
  console.log('🧪 Testing Shared FireGeo Client (as used by Dashboard)...');
  
  try {
    // Create client exactly as dashboard does
    const apiUrl = 'http://localhost:3001';
    const apiSecret = 'RobynnDevSecretKey';
    const client = new FireGeoApiClient(apiUrl, apiSecret);

    console.log('\n1️⃣ Testing health check...');
    const healthCheck = await client.healthCheck();
    console.log('✅ Health check success:', healthCheck.success);

    console.log('\n2️⃣ Testing website scraping with dummy token...');
    const scrapeResult = await client.scrapeWebsite('https://example.com', 'dummy-token');
    console.log('✅ Scrape success:', scrapeResult.company.name);
    console.log('   URL received by API:', scrapeResult.company.url);

    console.log('\n3️⃣ Testing brand analysis...');
    const analysisRequest = {
      company: scrapeResult.company,
      prompts: ['Best tools for developers?'],
      competitors: [{ name: 'Google' }]
    };

    try {
      const analysisResult = await client.startBrandAnalysis(analysisRequest, 'dummy-token');
      console.log('✅ Analysis started:', analysisResult.id);
    } catch (error) {
      // Expected to fail due to streaming response
      if (error.message.includes('Unexpected token')) {
        console.log('✅ Analysis endpoint responding (streaming response expected)');
      } else {
        throw error;
      }
    }

    console.log('\n🎉 Shared client is working correctly! The dashboard should work now.');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Stack:', error.stack);
  }
}

testSharedClient();
