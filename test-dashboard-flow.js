#!/usr/bin/env node

const API_URL = 'http://localhost:3001';
const API_SECRET = 'RobynnDevSecretKey';

async function testDashboardFlow() {
  console.log('🧪 Testing Dashboard Brand Monitor Flow...');
  
  try {
    // Step 1: Test authentication bridge (what dashboard does first)
    console.log('\n1️⃣ Testing authentication bridge...');
    const authResponse = await fetch(`${API_URL}/api/auth/bridge`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Secret': API_SECRET,
      }
    });

    if (!authResponse.ok) {
      console.error('❌ Auth bridge failed:', authResponse.status);
      return;
    }

    const authData = await authResponse.json();
    console.log('✅ Auth bridge success:', authData.success);

    // Step 2: Test scraping (what dashboard does when user enters URL)
    console.log('\n2️⃣ Testing website scraping...');
    const scrapeResponse = await fetch(`${API_URL}/api/brand-monitor/scrape`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Secret': API_SECRET,
        'Authorization': 'Bearer dummy-token'
      },
      body: JSON.stringify({
        url: 'https://example.com',
        maxAge: *********
      })
    });

    if (!scrapeResponse.ok) {
      console.error('❌ Scrape failed:', scrapeResponse.status);
      const errorText = await scrapeResponse.text();
      console.error('Error response:', errorText);
      return;
    }

    const scrapeData = await scrapeResponse.json();
    console.log('✅ Scrape success:', scrapeData.company.name);

    // Step 3: Test brand analysis (what dashboard does after scraping)
    console.log('\n3️⃣ Testing brand analysis...');
    const analysisResponse = await fetch(`${API_URL}/api/brand-monitor/analyze`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Secret': API_SECRET,
        'Authorization': 'Bearer dummy-token'
      },
      body: JSON.stringify({
        company: scrapeData.company,
        prompts: ['Best tools for developers?', 'Top tech companies?'],
        competitors: [{ name: 'Google' }, { name: 'Microsoft' }]
      })
    });

    if (!analysisResponse.ok) {
      console.error('❌ Analysis failed:', analysisResponse.status);
      const errorText = await analysisResponse.text();
      console.error('Error response:', errorText);
      return;
    }

    const analysisData = await analysisResponse.json();
    console.log('✅ Analysis started:', analysisData.id);

    console.log('\n🎉 All tests passed! Dashboard flow is working correctly.');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testDashboardFlow();
