#!/usr/bin/env node

const API_URL = 'http://localhost:3001';
const API_SECRET = 'RobynnDevSecretKey';

async function testScrapeEndpoint() {
  console.log('🧪 Testing FireGeo API scrape endpoint...');
  
  try {
    const response = await fetch(`${API_URL}/api/brand-monitor/scrape`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Secret': API_SECRET,
        'Authorization': 'Bearer dummy-token'
      },
      body: JSON.stringify({
        url: 'https://example.com',
        maxAge: 604800000
      })
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const responseText = await response.text();
    console.log('Response body:', responseText);
    
    if (!response.ok) {
      console.error('❌ Request failed');
      return;
    }
    
    const data = JSON.parse(responseText);
    console.log('✅ Success:', data);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testScrapeEndpoint();
