#!/usr/bin/env node

// Test script to simulate dashboard form submission to FireGeo API
// This tests the integration between dashboard and FireGeo API

const API_URL = 'http://localhost:3001';
const API_SECRET = 'RobynnDevSecretKey';

async function testDashboardIntegration() {
  console.log('🧪 Testing Dashboard -> FireGeo API Integration\n');

  try {
    // Step 1: Test scraping (what dashboard does first)
    console.log('1️⃣ Testing website scraping...');
    const scrapeResponse = await fetch(`${API_URL}/api/brand-monitor/scrape`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Secret': API_SECRET,
      },
      body: JSON.stringify({
        url: 'https://example.com',
        maxAge: *********
      })
    });

    if (!scrapeResponse.ok) {
      throw new Error(`Scrape failed: ${scrapeResponse.status}`);
    }

    const scrapeData = await scrapeResponse.json();
    console.log('✅ Scraping successful');
    console.log('   Company:', scrapeData.company.name);
    console.log('   URL:', scrapeData.company.url);

    // Step 2: Test analysis (what dashboard does with scraped data)
    console.log('\n2️⃣ Testing brand analysis...');
    const analysisPayload = {
      company: scrapeData.company,
      prompts: ['What are the best technology companies?'],
      competitors: [{ name: 'Test Competitor' }]
    };

    console.log('📤 Sending analysis request:');
    console.log(JSON.stringify(analysisPayload, null, 2));

    const analysisResponse = await fetch(`${API_URL}/api/brand-monitor/analyze`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Secret': API_SECRET,
      },
      body: JSON.stringify(analysisPayload)
    });

    if (!analysisResponse.ok) {
      const errorText = await analysisResponse.text();
      throw new Error(`Analysis failed: ${analysisResponse.status} - ${errorText}`);
    }

    console.log('✅ Analysis request accepted');
    console.log('📥 Receiving SSE stream...');

    // Read the SSE stream
    const reader = analysisResponse.body.getReader();
    const decoder = new TextDecoder();
    let eventCount = 0;

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (line.startsWith('event:')) {
          eventCount++;
          const eventType = line.substring(6).trim();
          console.log(`   📡 Event ${eventCount}: ${eventType}`);
        }
        if (line.startsWith('data:') && line.includes('"type":"complete"')) {
          console.log('✅ Analysis completed successfully!');
          reader.cancel();
          return;
        }
      }

      // Limit to prevent infinite loop
      if (eventCount > 20) {
        console.log('✅ Received sufficient events, stopping...');
        reader.cancel();
        break;
      }
    }

    console.log('\n🎉 Integration test completed successfully!');

  } catch (error) {
    console.error('\n❌ Integration test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testDashboardIntegration();
